nohup: ignoring input

> backend@1.0.0 dev
> nodemon --exec tsx src/server.ts

[33m[nodemon] 3.1.10[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): *.*[39m
[33m[nodemon] watching extensions: ts,json[39m
[32m[nodemon] starting `tsx src/server.ts`[39m
node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: EBADF: bad file descriptor, read
Emitted 'error' event on ReadStream instance at:
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at errorOrDestroy (node:internal/streams/destroy:239:7)
    at node:internal/fs/streams:272:9
    at FSReqCallback.wrapper [as oncomplete] (node:fs:671:5) {
  errno: -9,
  code: 'EBADF',
  syscall: 'read'
}

Node.js v22.17.0
[06:28:10 UTC] ERROR: listen EADDRINUSE: address already in use 0.0.0.0:8080
    err: {
      "type": "Error",
      "message": "listen EADDRINUSE: address already in use 0.0.0.0:8080",
      "stack":
          Error: listen EADDRINUSE: address already in use 0.0.0.0:8080
              at Server.setupListenHandle [as _listen2] (node:net:1940:16)
              at listenInCluster (node:net:1997:12)
              at node:net:2206:7
              at process.processTicksAndRejections (node:internal/process/task_queues:90:21)
      "code": "EADDRINUSE",
      "errno": -98,
      "syscall": "listen",
      "address": "0.0.0.0",
      "port": 8080
    }
