{"name": "@fastify/send", "description": "Better streaming static file server with Range and conditional-GET support", "version": "4.1.0", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/fastify/send.git"}, "bugs": {"url": "https://github.com/fastify/send/issues"}, "homepage": "https://github.com/fastify/send#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "keywords": ["static", "file", "server"], "dependencies": {"@lukeed/ms": "^2.0.2", "escape-html": "~1.0.3", "fast-decode-uri-component": "^1.0.1", "http-errors": "^2.0.0", "mime": "^3"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@types/node": "^22.0.0", "after": "0.8.2", "benchmark": "^2.1.4", "c8": "^10.1.3", "eslint": "^9.17.0", "neostandard": "^0.12.0", "supertest": "6.3.4", "tsd": "^0.32.0"}, "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:coverage": "c8 --reporter html node --test", "test:typescript": "tsd", "test:unit": "c8 --100 node --test"}, "pre-commit": ["lint", "test"]}