/*!
 * send
 * Copyright(c) 2012 <PERSON><PERSON>
 * Copyright(c) 2014-2022 <PERSON>
 * MIT Licensed
 */
'use strict'
/**
 * Create a minimal HTML document.
 *
 * @param {string} title
 * @param {string} body
 * @private
 */
function createHtmlDocument (title, body) {
  const html = '<!DOCTYPE html>\n' +
    '<html lang="en">\n' +
    '<head>\n' +
    '<meta charset="utf-8">\n' +
    '<title>' + title + '</title>\n' +
    '</head>\n' +
    '<body>\n' +
    '<pre>' + body + '</pre>\n' +
    '</body>\n' +
    '</html>\n'

  return [html, Buffer.byteLength(html)]
}
exports.createHtmlDocument = createHtmlDocument
