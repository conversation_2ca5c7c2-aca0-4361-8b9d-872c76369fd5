name: Playwright Tests

on:
  workflow_dispatch:
  workflow_call:

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    timeout-minutes: 60

    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        persist-credentials: false
    - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
      with:
        check-latest: true
        node-version: lts/*
    - name: Install dependencies
      run: npm i
    - name: Install Playwright Browsers
      run: npx playwright@1 install chromium --with-deps
    - name: Run Playwright tests
      run: npx playwright@1 test
