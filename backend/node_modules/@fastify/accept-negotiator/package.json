{"name": "@fastify/accept-negotiator", "version": "2.0.1", "description": "a negotiator for the accept-headers", "type": "commonjs", "main": "index.js", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:unit": "c8 --100 node --test", "test:typescript": "tsd"}, "keywords": ["encoding", "negotiator", "accept-encoding", "accept", "http", "header"], "files": ["README.md", "LICENSE", "index.js", "types/index.d.ts"], "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@matteo.collina/tspl": "^0.1.1", "benchmark": "2.1.4", "c8": "^10.1.2", "eslint": "^9.17.0", "neostandard": "^0.12.0", "tsd": "^0.31.0"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/accept-negotiator.git"}, "bugs": {"url": "https://github.com/fastify/accept-negotiator/issues"}, "homepage": "https://github.com/fastify/accept-negotiator#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}]}