{"name": "@fastify/env", "version": "5.0.2", "description": "Fastify plugin to check environment variables", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"coverage": "cross-env VALUE_FROM_ENV=pippo c8 --reporter=html node --test", "lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "c8 --100 cross-env VALUE_FROM_ENV=pippo node --test"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-env.git"}, "keywords": ["fastify"], "author": "<PERSON><PERSON><PERSON> - @allevo", "contributors": [{"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-env/issues"}, "homepage": "https://github.com/fastify/fastify-env#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "dependencies": {"env-schema": "^6.0.0", "fastify-plugin": "^5.0.0"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@types/node": "^22.0.0", "c8": "^10.1.2", "cross-env": "^7.0.3", "eslint": "^9.17.0", "fastify": "^5.0.0", "neostandard": "^0.12.0", "tsd": "^0.31.0"}, "pre-commit": ["lint", "test"], "tsd": {"directory": "test/types"}, "publishConfig": {"access": "public"}}