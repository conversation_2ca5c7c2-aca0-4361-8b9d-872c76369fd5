{"name": "@fastify/helmet", "version": "13.0.1", "description": "Important security headers for Fastify", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "c8 --100 node --test"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-helmet.git"}, "keywords": ["fastify", "helmet", "security", "headers", "x-frame-options", "csp", "hsts", "clickjack"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "url": "http://delved.org"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://maksim.dev"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-helmet/issues"}, "homepage": "https://github.com/fastify/fastify-helmet#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@types/node": "^22.0.0", "c8": "^10.1.2", "eslint": "^9.17.0", "fastify": "^5.0.0", "neostandard": "^0.12.0", "tsd": "^0.31.0"}, "dependencies": {"fastify-plugin": "^5.0.0", "helmet": "^8.0.0"}, "tsd": {"directory": "test/types"}, "publishConfig": {"access": "public"}}