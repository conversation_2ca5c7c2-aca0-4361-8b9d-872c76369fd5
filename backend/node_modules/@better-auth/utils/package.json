{"name": "@better-auth/utils", "version": "0.2.5", "license": "MIT", "description": "A collection of utilities for better-auth", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "keywords": ["auth", "utils", "typescript", "better-auth", "better-auth-utils"], "author": "Be<PERSON><PERSON> Engida", "repository": {"type": "git", "url": "https://github.com/better-auth/utils"}, "dependencies": {"typescript": "^5.8.2", "uncrypto": "^0.1.3"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.10.1", "bumpp": "^9.9.0", "happy-dom": "^15.11.7", "unbuild": "^2.0.0", "vitest": "^2.1.8"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./base32": {"import": "./dist/base32.mjs", "require": "./dist/base32.cjs"}, "./base64": {"import": "./dist/base64.mjs", "require": "./dist/base64.cjs"}, "./binary": {"import": "./dist/binary.mjs", "require": "./dist/binary.cjs"}, "./hash": {"import": "./dist/hash.mjs", "require": "./dist/hash.cjs"}, "./ecdsa": {"import": "./dist/ecdsa.mjs", "require": "./dist/ecdsa.cjs"}, "./hex": {"import": "./dist/hex.mjs", "require": "./dist/hex.cjs"}, "./hmac": {"import": "./dist/hmac.mjs", "require": "./dist/hmac.cjs"}, "./otp": {"import": "./dist/otp.mjs", "require": "./dist/otp.cjs"}, "./random": {"import": "./dist/random.mjs", "require": "./dist/random.cjs"}, "./rsa": {"import": "./dist/rsa.mjs", "require": "./dist/rsa.cjs"}}, "files": ["dist"], "scripts": {"test": "vitest", "typecheck": "tsc --noEmit", "build": "unbuild", "lint:fix": "biome check . --write"}}