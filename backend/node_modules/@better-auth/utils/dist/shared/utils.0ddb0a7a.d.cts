type TypedArray = Uint8Array | Int8Array | Uint16Array | Int16Array | Uint32Array | Int32Array | Float32Array | Float64Array | BigInt64Array | BigUint64Array;
type SHAFamily = "SHA-1" | "SHA-256" | "SHA-384" | "SHA-512";
type EncodingFormat = "hex" | "base64" | "base64url" | "base64urlnopad" | "none";
type ECDSACurve = "P-256" | "P-384" | "P-521";
type ExportKeyFormat = "jwk" | "spki" | "pkcs8" | "raw";

export type { EncodingFormat as E, SHAFamily as S, TypedArray as T, ECDSACurve as a, ExportKeyFormat as b };
