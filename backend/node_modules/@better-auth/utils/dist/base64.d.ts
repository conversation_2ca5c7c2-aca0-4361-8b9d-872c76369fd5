import { T as TypedArray } from './shared/utils.0ddb0a7a.js';

declare const base64: {
    encode(data: ArrayBuffer | TypedArray | string, options?: {
        padding?: boolean;
    }): string;
    decode(data: string | ArrayBuffer | TypedArray): Uint8Array<ArrayBufferLike>;
};
declare const base64Url: {
    encode(data: ArrayBuffer | TypedArray | string, options?: {
        padding?: boolean;
    }): string;
    decode(data: string): Uint8Array<ArrayBufferLike>;
};

export { base64, base64Url };
