
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userType: 'userType',
  fullName: 'fullName',
  email: 'email',
  phonePrefix: 'phonePrefix',
  phone: 'phone',
  role: 'role',
  invitedBy: 'invitedBy',
  isInvitationAccepted: 'isInvitationAccepted',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  emailVerified: 'emailVerified',
  lastSigninAt: 'lastSigninAt',
  legacyId: 'legacyId',
  isActive: 'isActive'
};

exports.Prisma.AuthSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  token: 'token',
  expiresAt: 'expiresAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OtpSessionScalarFieldEnum = {
  id: 'id',
  email: 'email',
  otpCode: 'otpCode',
  expiresAt: 'expiresAt',
  attempts: 'attempts',
  verified: 'verified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BrandingSettingsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  brandLogo: 'brandLogo',
  primaryColor: 'primaryColor',
  secondaryColor: 'secondaryColor',
  accentColor: 'accentColor',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AppScalarFieldEnum = {
  id: 'id',
  name: 'name',
  logo: 'logo',
  subtitle: 'subtitle',
  description: 'description',
  category: 'category',
  isEnabled: 'isEnabled',
  requiresAdminApproval: 'requiresAdminApproval',
  status: 'status',
  features: 'features',
  documentation: 'documentation',
  settings: 'settings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserAppPermissionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  appId: 'appId',
  canView: 'canView',
  canEnable: 'canEnable',
  canDisable: 'canDisable',
  requiresApproval: 'requiresApproval',
  approvalStatus: 'approvalStatus',
  isEnabled: 'isEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserProfileScalarFieldEnum = {
  userId: 'userId',
  governmentIdNumber: 'governmentIdNumber',
  governmentIdImage: 'governmentIdImage',
  certificateNumber: 'certificateNumber',
  documentImage: 'documentImage',
  crNumber: 'crNumber',
  unifiedNationalNumber: 'unifiedNationalNumber',
  crDocument: 'crDocument',
  vatId: 'vatId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BankDetailScalarFieldEnum = {
  id: 'id',
  profileType: 'profileType',
  userId: 'userId',
  bankName: 'bankName',
  accountName: 'accountName',
  iban: 'iban',
  swiftCode: 'swiftCode',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  planId: 'planId'
};

exports.Prisma.UserInvitationScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  email: 'email',
  phone: 'phone',
  deliveryMethod: 'deliveryMethod',
  invitedBy: 'invitedBy',
  role: 'role',
  token: 'token',
  isAccepted: 'isAccepted',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CountryScalarFieldEnum = {
  id: 'id',
  code: 'code',
  codeIso3: 'codeIso3',
  name: 'name',
  phonePrefix: 'phonePrefix',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CityScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  nameAr: 'nameAr',
  countryCode: 'countryCode',
  lat: 'lat',
  long: 'long',
  region: 'region',
  type: 'type',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AddressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  name: 'name',
  phone: 'phone',
  address1: 'address1',
  address2: 'address2',
  cityName: 'cityName',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  cityId: 'cityId',
  lat: 'lat',
  long: 'long',
  isDefault: 'isDefault',
  type: 'type',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EInvoiceSettingScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  allowedCarrierCodes: 'allowedCarrierCodes',
  allowedPayments: 'allowedPayments',
  defaultAddressId: 'defaultAddressId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CarrierScalarFieldEnum = {
  id: 'id',
  carrierCode: 'carrierCode',
  logoUrl: 'logoUrl',
  displayOrder: 'displayOrder',
  maxWeight: 'maxWeight',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BranchScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  station: 'station',
  url: 'url',
  lat: 'lat',
  long: 'long',
  openTime: 'openTime',
  closeTime: 'closeTime',
  contactName: 'contactName',
  contactNumber: 'contactNumber',
  isActive: 'isActive',
  type: 'type',
  additionalInfo: 'additionalInfo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  cityCode: 'cityCode',
  carrierCode: 'carrierCode'
};

exports.Prisma.CarrierCityServiceScalarFieldEnum = {
  id: 'id',
  pickupType: 'pickupType',
  dropoffType: 'dropoffType',
  notes: 'notes',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  cityCode: 'cityCode',
  carrierCode: 'carrierCode'
};

exports.Prisma.CarrierServiceScalarFieldEnum = {
  id: 'id',
  carrierCode: 'carrierCode',
  name: 'name',
  pickupType: 'pickupType',
  dropoffType: 'dropoffType',
  region: 'region',
  pickupTime: 'pickupTime',
  transitTime: 'transitTime',
  deliveryTime: 'deliveryTime',
  deliveryDays: 'deliveryDays',
  pickupDays: 'pickupDays',
  dimensionalLimit: 'dimensionalLimit',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ZoneScalarFieldEnum = {
  id: 'id',
  countryCode: 'countryCode',
  carrierCode: 'carrierCode',
  zoneCode: 'zoneCode',
  zoneName: 'zoneName',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ZoneToCityScalarFieldEnum = {
  zoneId: 'zoneId',
  cityId: 'cityId'
};

exports.Prisma.CostScalarFieldEnum = {
  id: 'id',
  fromCountryCode: 'fromCountryCode',
  fromZone: 'fromZone',
  toCountryCode: 'toCountryCode',
  toZone: 'toZone',
  carrierCode: 'carrierCode',
  fromWeight: 'fromWeight',
  toWeight: 'toWeight',
  rateType: 'rateType',
  costAmount: 'costAmount',
  baseFee: 'baseFee',
  currency: 'currency',
  validFrom: 'validFrom',
  validTo: 'validTo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RateScalarFieldEnum = {
  id: 'id',
  fromCountryCode: 'fromCountryCode',
  fromZone: 'fromZone',
  toCountryCode: 'toCountryCode',
  toZone: 'toZone',
  carrierCode: 'carrierCode',
  fromWeight: 'fromWeight',
  toWeight: 'toWeight',
  rateType: 'rateType',
  rateAmount: 'rateAmount',
  baseFee: 'baseFee',
  currency: 'currency',
  planId: 'planId',
  deliveryDays: 'deliveryDays',
  validFrom: 'validFrom',
  validTo: 'validTo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  organizationId: 'organizationId'
};

exports.Prisma.PlanScalarFieldEnum = {
  id: 'id',
  name: 'name',
  code: 'code',
  description: 'description',
  price: 'price',
  durationInMonths: 'durationInMonths',
  isActive: 'isActive',
  upgradeTo: 'upgradeTo',
  trialPeriodDays: 'trialPeriodDays',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlanServiceScalarFieldEnum = {
  id: 'id',
  planId: 'planId',
  serviceId: 'serviceId',
  enabled: 'enabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserServiceOverrideScalarFieldEnum = {
  userId: 'userId',
  serviceId: 'serviceId',
  isEnabled: 'isEnabled',
  reason: 'reason',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CouponScalarFieldEnum = {
  id: 'id',
  code: 'code',
  discountType: 'discountType',
  discountValue: 'discountValue',
  usageLimit: 'usageLimit',
  usedCount: 'usedCount',
  maxUsagePerUser: 'maxUsagePerUser',
  minOrderValue: 'minOrderValue',
  planId: 'planId',
  expiresAt: 'expiresAt',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CouponUsageScalarFieldEnum = {
  id: 'id',
  couponId: 'couponId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WalletScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  balance: 'balance',
  currency: 'currency',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  debitType: 'debitType',
  debitId: 'debitId',
  creditType: 'creditType',
  creditId: 'creditId',
  paymentGateway: 'paymentGateway',
  gatewayTransactionId: 'gatewayTransactionId',
  gatewayResponseCode: 'gatewayResponseCode',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WalletTransactionScalarFieldEnum = {
  id: 'id',
  walletId: 'walletId',
  transactionId: 'transactionId',
  type: 'type',
  amount: 'amount',
  balanceBefore: 'balanceBefore',
  balanceAfter: 'balanceAfter',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  externalId: 'externalId',
  platformId: 'platformId',
  name: 'name',
  sku: 'sku',
  barcode: 'barcode',
  description: 'description',
  price: 'price',
  currency: 'currency',
  hsCode: 'hsCode',
  countryOfOrigin: 'countryOfOrigin',
  weightGrams: 'weightGrams',
  lengthCm: 'lengthCm',
  widthCm: 'widthCm',
  heightCm: 'heightCm',
  isActive: 'isActive',
  syncedAt: 'syncedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductVariantScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  userId: 'userId',
  name: 'name',
  sku: 'sku',
  barcode: 'barcode',
  price: 'price',
  weightGrams: 'weightGrams',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductImageScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  userId: 'userId',
  imageUrl: 'imageUrl',
  isPrimary: 'isPrimary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WarehouseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  code: 'code',
  address1: 'address1',
  address2: 'address2',
  cityName: 'cityName',
  state: 'state',
  zip: 'zip',
  country: 'country',
  cityId: 'cityId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isActive: 'isActive'
};

exports.Prisma.WarehouseBinScalarFieldEnum = {
  id: 'id',
  warehouseId: 'warehouseId',
  userId: 'userId',
  code: 'code',
  zone: 'zone',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductInventoryScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  variantId: 'variantId',
  warehouseId: 'warehouseId',
  binId: 'binId',
  userId: 'userId',
  batchNumber: 'batchNumber',
  expirationDate: 'expirationDate',
  quantity: 'quantity',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  userId: 'userId',
  type: 'type',
  fromAddress: 'fromAddress',
  toAddress: 'toAddress',
  platformOrderId: 'platformOrderId',
  status: 'status',
  description: 'description',
  paymentStatus: 'paymentStatus',
  trackingNumber: 'trackingNumber',
  declaredValue: 'declaredValue',
  codAmount: 'codAmount',
  source: 'source',
  carrierCode: 'carrierCode',
  metadata: 'metadata',
  addressId: 'addressId',
  recipientPhone: 'recipientPhone',
  linkToken: 'linkToken',
  linkExpiresAt: 'linkExpiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderId: 'orderId',
  productId: 'productId',
  productName: 'productName',
  productSku: 'productSku',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderChargeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderId: 'orderId',
  name: 'name',
  type: 'type',
  amount: 'amount',
  currency: 'currency',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ParcelScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderId: 'orderId',
  length: 'length',
  width: 'width',
  height: 'height',
  actualWeight: 'actualWeight',
  dimensionalWeight: 'dimensionalWeight',
  finalWeight: 'finalWeight',
  trackingNumber: 'trackingNumber',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrackingDataScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  status: 'status',
  checkpoint: 'checkpoint',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlatformScalarFieldEnum = {
  id: 'id',
  code: 'code',
  name: 'name',
  logoUrl: 'logoUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlatformIntegrationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  platformId: 'platformId',
  externalShopId: 'externalShopId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  tokenExpiresAt: 'tokenExpiresAt',
  shopName: 'shopName',
  connectedAt: 'connectedAt',
  isActive: 'isActive',
  updatedAt: 'updatedAt'
};

exports.Prisma.PlatformOrderScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  platformId: 'platformId',
  externalOrderId: 'externalOrderId',
  status: 'status',
  customerName: 'customerName',
  customerAddressId: 'customerAddressId',
  items: 'items',
  rawPayload: 'rawPayload',
  syncedAt: 'syncedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductSyncLogScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  platformId: 'platformId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  syncStatus: 'syncStatus',
  errorMessage: 'errorMessage'
};

exports.Prisma.ActivityLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  tableName: 'tableName',
  recordId: 'recordId',
  action: 'action',
  message: 'message',
  level: 'level',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.AuditTrailScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  tableName: 'tableName',
  recordId: 'recordId',
  fieldChanged: 'fieldChanged',
  oldValue: 'oldValue',
  newValue: 'newValue',
  createdAt: 'createdAt'
};

exports.Prisma.WebhookLogScalarFieldEnum = {
  id: 'id',
  payload: 'payload',
  source: 'source',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ContentTranslationScalarFieldEnum = {
  id: 'id',
  tableName: 'tableName',
  recordId: 'recordId',
  fieldName: 'fieldName',
  languageCode: 'languageCode',
  translatedValue: 'translatedValue',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserType = exports.$Enums.UserType = {
  PERSONAL: 'PERSONAL',
  FREELANCER: 'FREELANCER',
  COMPANY: 'COMPANY'
};

exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  MEMBER: 'MEMBER'
};

exports.AppStatus = exports.$Enums.AppStatus = {
  ACTIVE: 'ACTIVE',
  DISABLED: 'DISABLED',
  PENDING_REVIEW: 'PENDING_REVIEW'
};

exports.ApprovalStatus = exports.$Enums.ApprovalStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.CityType = exports.$Enums.CityType = {
  CITY: 'CITY',
  TOWN: 'TOWN',
  VILLAGE: 'VILLAGE'
};

exports.AddressType = exports.$Enums.AddressType = {
  SENDER: 'SENDER',
  RECEIVER: 'RECEIVER',
  BILLING: 'BILLING',
  COMPANY: 'COMPANY',
  CUSTOMER: 'CUSTOMER'
};

exports.BranchType = exports.$Enums.BranchType = {
  BRANCH: 'BRANCH',
  LOCKER: 'LOCKER',
  DROPOFF_POINT: 'DROPOFF_POINT'
};

exports.ServiceAvailabilityType = exports.$Enums.ServiceAvailabilityType = {
  BRANCH: 'BRANCH',
  LOCKER: 'LOCKER',
  DOOR: 'DOOR'
};

exports.OrderType = exports.$Enums.OrderType = {
  SHIPMENT: 'SHIPMENT',
  E_INVOICE_SHIPMENT: 'E_INVOICE_SHIPMENT',
  E_INVOICE_RETURN: 'E_INVOICE_RETURN',
  E_INVOICE_SALE: 'E_INVOICE_SALE'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  WAITING_FOR_PICKUP: 'WAITING_FOR_PICKUP',
  IN_TRANSIT: 'IN_TRANSIT',
  DELIVERED: 'DELIVERED',
  ONHOLD: 'ONHOLD',
  CANCELLED: 'CANCELLED'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  PAID: 'PAID',
  UNPAID: 'UNPAID'
};

exports.Prisma.ModelName = {
  User: 'User',
  AuthSession: 'AuthSession',
  OtpSession: 'OtpSession',
  BrandingSettings: 'BrandingSettings',
  App: 'App',
  UserAppPermission: 'UserAppPermission',
  UserProfile: 'UserProfile',
  BankDetail: 'BankDetail',
  Organization: 'Organization',
  UserInvitation: 'UserInvitation',
  Country: 'Country',
  City: 'City',
  Address: 'Address',
  EInvoiceSetting: 'EInvoiceSetting',
  Carrier: 'Carrier',
  Branch: 'Branch',
  CarrierCityService: 'CarrierCityService',
  CarrierService: 'CarrierService',
  Zone: 'Zone',
  ZoneToCity: 'ZoneToCity',
  Cost: 'Cost',
  Rate: 'Rate',
  Plan: 'Plan',
  Service: 'Service',
  PlanService: 'PlanService',
  UserServiceOverride: 'UserServiceOverride',
  Coupon: 'Coupon',
  CouponUsage: 'CouponUsage',
  Wallet: 'Wallet',
  Transaction: 'Transaction',
  WalletTransaction: 'WalletTransaction',
  Product: 'Product',
  ProductVariant: 'ProductVariant',
  ProductImage: 'ProductImage',
  Warehouse: 'Warehouse',
  WarehouseBin: 'WarehouseBin',
  ProductInventory: 'ProductInventory',
  Order: 'Order',
  OrderItem: 'OrderItem',
  OrderCharge: 'OrderCharge',
  Parcel: 'Parcel',
  TrackingData: 'TrackingData',
  Platform: 'Platform',
  PlatformIntegration: 'PlatformIntegration',
  PlatformOrder: 'PlatformOrder',
  ProductSyncLog: 'ProductSyncLog',
  ActivityLog: 'ActivityLog',
  AuditTrail: 'AuditTrail',
  WebhookLog: 'WebhookLog',
  ContentTranslation: 'ContentTranslation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
