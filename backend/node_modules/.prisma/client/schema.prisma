// ========================================================================
// --- Final Consolidated Prisma Schema
// --- Last Updated: July 14, 2025
// --- Organized by functional domains with descriptive comments
// --- Refactored to use PascalCase for models and camelCase for fields.
// ========================================================================

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ========================================================================
// --- ENUMS ---
// ========================================================================

// User account types for different business models
enum UserType {
  PERSONAL
  FREELANCER
  COMPANY
}

// User roles for different permissions
enum UserRole {
  ADMIN
  MEMBER
}

// Order types for different purposes
enum OrderType {
  SHIPMENT
  E_INVOICE_SHIPMENT
  E_INVOICE_RETURN
  E_INVOICE_SALE
}

// Physical location types for carrier branches
enum BranchType {
  BRANCH
  LOCKER
  DROPOFF_POINT
}

// Payment status for orders
enum PaymentStatus {
  PAID
  UNPAID
}

// City types for statistics purposes
enum CityType {
  CITY
  TOWN
  VILLAGE
}

// Order status for orders
enum OrderStatus {
  PENDING
  WAITING_FOR_PICKUP
  IN_TRANSIT
  DELIVERED
  ONHOLD
  CANCELLED
}

// Service availability options for carrier services in cities
enum ServiceAvailabilityType {
  BRANCH
  LOCKER
  DOOR
}

// Address types for different purposes
enum AddressType {
  SENDER
  RECEIVER
  BILLING
  COMPANY
  CUSTOMER
}

// App status for marketplace apps
enum AppStatus {
  ACTIVE
  DISABLED
  PENDING_REVIEW
}

// Approval status for user app permissions
enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

// ========================================================================
// --- USER & AUTHENTICATION MODELS ---
// ========================================================================

// Core user model representing all types of users in the system
model User {
  id                   String    @id @default(uuid()) @db.Uuid
  organizationId       String?   @db.Uuid
  userType             UserType
  fullName             String?
  email                String    @unique
  phonePrefix          String? // e.g., '+966'
  phone                String?   @unique
  role                 UserRole  @default(MEMBER)
  invitedBy            String?   @db.Uuid
  isInvitationAccepted Boolean?  @default(false)
  isVerified           Boolean?  @default(false)
  createdAt            DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt            DateTime? @updatedAt @db.Timestamptz(6)
  emailVerified        Boolean?  @default(false)
  lastSigninAt         DateTime? @db.Timestamptz(6)
  legacyId             String?   @unique // To store old user id from MySQL
  isActive             Boolean?  @default(true)

  // --- Relationships ---
  addresses            Address[]
  auditTrails          AuditTrail[]
  authSessions         AuthSession[]
  bankDetails          BankDetail[]
  warehouseBins        WarehouseBin[]
  couponUsages         CouponUsage[]
  activityLogs         ActivityLog[]
  orderCharges         OrderCharge[]
  orderItems           OrderItem[]
  orders               Order[]
  parcels              Parcel[]
  platformIntegrations PlatformIntegration[]
  platformOrders       PlatformOrder[]
  productImages        ProductImage[]
  productInventories   ProductInventory[]
  productSyncLogs      ProductSyncLog[]
  productVariants      ProductVariant[]
  products             Product[]
  rates                Rate[]
  transactions         Transaction[]
  userInvitations      UserInvitation[]
  userServiceOverrides UserServiceOverride[]
  invitedByUser        User?                 @relation("UserToUser", fields: [invitedBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
  invitedUsers         User[]                @relation("UserToUser")
  organization         Organization?         @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  wallet               Wallet?
  warehouses           Warehouse[]
  einvoiceSetting      EInvoiceSetting?
  profile              UserProfile?
  brandingSettings     BrandingSettings?
  userAppPermissions   UserAppPermission[]

  @@index([phone])
  @@index([organizationId])
}

// Authentication session tokens for user sessions
model AuthSession {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @db.Uuid
  token     String   @unique
  expiresAt DateTime @db.Timestamptz(6)
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([userId])
}

// OTP verification sessions
model OtpSession {
  id        String   @id @default(uuid()) @db.Uuid
  email     String
  otpCode   String
  expiresAt DateTime @db.Timestamptz(6)
  attempts  Int      @default(0)
  verified  Boolean  @default(false)
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  @@index([email])
  @@index([otpCode])
}

// Branding settings for user's brand customization
model BrandingSettings {
  id     String @id @default(uuid()) @db.Uuid
  userId String @unique @db.Uuid
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Branding fields
  brandLogo      String? // URL to brand logo image
  primaryColor   String  @default("#000000") // Hex color code
  secondaryColor String  @default("#666666") // Hex color code
  accentColor    String  @default("#0066CC") // Hex color code

  // Metadata
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  @@index([userId])
}

// ========================================================================
// --- APP MARKETPLACE MODELS ---
// ========================================================================

// Marketplace apps available for integration
model App {
  id                    String    @id @default(uuid()) @db.Uuid
  name                  String
  logo                  String // emoji or image URL
  subtitle              String
  description           String?
  category              String?
  isEnabled             Boolean   @default(false)
  requiresAdminApproval Boolean   @default(false)
  status                AppStatus @default(DISABLED)
  features              String[]  @default([])
  documentation         Json? // JSON object with userGuide, developerGuide, supportContact
  settings              Json? // JSON object for app-specific settings
  createdAt             DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt             DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  userAppPermissions UserAppPermission[]

  @@index([category])
  @@index([status])
}

// User-specific permissions and settings for apps
model UserAppPermission {
  id               String          @id @default(uuid()) @db.Uuid
  userId           String          @db.Uuid
  appId            String          @db.Uuid
  canView          Boolean         @default(true)
  canEnable        Boolean         @default(false)
  canDisable       Boolean         @default(false)
  requiresApproval Boolean         @default(false)
  approvalStatus   ApprovalStatus?
  isEnabled        Boolean         @default(false)
  createdAt        DateTime        @default(now()) @db.Timestamptz(6)
  updatedAt        DateTime        @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  app  App  @relation(fields: [appId], references: [id], onDelete: Cascade)

  @@unique([userId, appId])
  @@index([userId])
  @@index([appId])
  @@index([approvalStatus])
}

// ========================================================================
// --- USER PROFILE MODELS ---
// ========================================================================

// Consolidated profile for all user types (Personal, Freelancer, Company)
model UserProfile {
  userId String @id @db.Uuid

  // --- Personal & Freelancer Fields ---
  governmentIdNumber String? // National ID or government-issued ID number.
  governmentIdImage  String? // Image of the national or government ID.

  // --- Freelancer-Specific Fields ---
  certificateNumber String? // Freelancer certificate or license number.
  documentImage     String? // Image of the freelancer document/certificate.

  // --- Company-Specific Fields ---
  crNumber              String? // Commercial Registration (CR) number for the company.
  unifiedNationalNumber String? // Company's unique 10-digit national identifier.
  crDocument            String? // Image of the Commercial Registration (CR) document.
  vatId                 String? // VAT identification number for the company.

  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  // Establishes a one-to-one relationship back to the user.
  // onDelete: Cascade ensures the profile is deleted if the user is deleted.
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Bank account details for user payouts and payments
model BankDetail {
  id          String    @id @default(uuid()) @db.Uuid
  profileType String?
  userId      String    @db.Uuid
  bankName    String?
  accountName String?
  iban        String?
  swiftCode   String?
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user User @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

// ========================================================================
// --- ORGANIZATION & INVITATION MODELS ---
// ========================================================================

// Organizations that group multiple users together
model Organization {
  id        String    @id @default(uuid()) @db.Uuid
  name      String
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)
  planId    String?   @db.Uuid

  // --- Relationships ---
  plan            Plan?            @relation(fields: [planId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  rates           Rate[]
  userInvitations UserInvitation[]
  users           User[]
}

// Invitations to join organizations
model UserInvitation {
  id             String    @id @default(uuid()) @db.Uuid
  organizationId String    @db.Uuid
  email          String?
  phone          String?
  deliveryMethod String?   @default("email")
  invitedBy      String    @db.Uuid
  role           String    @default("member")
  token          String    @unique
  isAccepted     Boolean?  @default(false)
  expiresAt      DateTime  @db.Timestamptz(6)
  createdAt      DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user         User         @relation(fields: [invitedBy], references: [id], onDelete: NoAction, onUpdate: NoAction)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([organizationId])
  @@index([token])
}

// ========================================================================
// --- LOCATION & ADDRESS MODELS ---
// ========================================================================

// Countries supported by the platform
model Country {
  id          String    @id @default(uuid()) @db.Uuid
  code        String    @unique @db.Char(2)
  codeIso3    String    @unique @db.Char(3)
  name        String
  phonePrefix String
  isActive    Boolean?  @default(true)
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  cities    City[]
  costsFrom Cost[] @relation("CostFromCountry")
  costsTo   Cost[] @relation("CostToCountry")
  ratesFrom Rate[] @relation("RateFromCountry")
  ratesTo   Rate[] @relation("RateToCountry")
  zones     Zone[]
}

// Cities within countries for shipping and addressing
model City {
  id          String    @id @default(uuid()) @db.Uuid
  code        String    @unique
  name        String
  nameAr      String?
  countryCode String    @db.Char(2)
  lat         Decimal?  @db.Decimal(11, 8)
  long        Decimal?  @db.Decimal(12, 8)
  region      String?
  type        CityType?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  country             Country              @relation(fields: [countryCode], references: [code])
  branches            Branch[]
  carrierCityServices CarrierCityService[]
  zoneCities          ZoneToCity[]
  warehouses          Warehouse[]
  addresses           Address[]
}

// Unified address model for all address types (sender, receiver, billing, company)
model Address {
  id         String      @id @default(uuid()) @db.Uuid
  userId     String?     @db.Uuid
  title      String?
  name       String?
  phone      String?
  address1   String?
  address2   String?
  cityName   String?
  state      String?
  postalCode String?
  country    String?
  cityId     String?     @db.Uuid
  lat        Decimal?    @db.Decimal(11, 8)
  long       Decimal?    @db.Decimal(12, 8)
  isDefault  Boolean?    @default(false)
  type       AddressType
  createdAt  DateTime    @default(now()) @db.Timestamptz(6)
  updatedAt  DateTime    @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user             User?             @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  city             City?             @relation(fields: [cityId], references: [id])
  platformOrders   PlatformOrder[]
  einvoiceSettings EInvoiceSetting[]

  @@index([type])
  @@index([userId])
}

// =======================================
// --- SETTINGS ---
// =======================================

// e-invoice settings
model EInvoiceSetting {
  id                  String   @id @default(uuid()) @db.Uuid
  userId              String   @unique @db.Uuid
  user                User     @relation(fields: [userId], references: [id])
  allowedCarrierCodes String[] // references carrier.carrierCode e.g. ["ARAMEX", "DHL"]
  allowedPayments     String[] // e.g. ["SHIPTAG_PAY", "COD"]
  defaultAddressId    String?  @db.Uuid
  defaultAddress      Address? @relation(fields: [defaultAddressId], references: [id])
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
}

// ========================================================================
// --- CARRIER & SHIPPING MODELS ---
// ========================================================================

// Shipping carriers (e.g., DHL, FedEx, local carriers)
model Carrier {
  id           String   @id @default(uuid()) @db.Uuid
  carrierCode  String   @unique
  logoUrl      String?
  displayOrder Int?     @default(0)
  maxWeight    Decimal? @db.Decimal(12, 3)
  createdAt    DateTime @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  branches            Branch[]
  carrierCityServices CarrierCityService[]
  zones               Zone[]
  carrierServices     CarrierService[]
  costs               Cost[]
  orders              Order[]
  rates               Rate[]
}

// Physical locations where carriers operate (branches, lockers, drop-off points)
model Branch {
  id             String     @id @default(uuid()) @db.Uuid
  name           String
  address        String
  station        String?
  url            String?
  lat            Decimal?   @db.Decimal(11, 8)
  long           Decimal?   @db.Decimal(12, 8)
  openTime       String?
  closeTime      String?
  contactName    String?
  contactNumber  String?
  isActive       Boolean    @default(true)
  type           BranchType @default(BRANCH)
  additionalInfo Json?
  createdAt      DateTime   @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime   @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  cityCode    String
  carrierCode String
  city        City    @relation(fields: [cityCode], references: [code])
  carrier     Carrier @relation(fields: [carrierCode], references: [carrierCode])

  @@index([cityCode])
  @@index([carrierCode])
}

// Service availability for carriers in specific cities
model CarrierCityService {
  id          String                    @id @default(uuid()) @db.Uuid
  pickupType  ServiceAvailabilityType[] @default([])
  dropoffType ServiceAvailabilityType[] @default([])
  notes       String?                   @db.Text
  isActive    Boolean                   @default(true)
  createdAt   DateTime                  @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime                  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  cityCode    String
  carrierCode String
  city        City    @relation(fields: [cityCode], references: [code])
  carrier     Carrier @relation(fields: [carrierCode], references: [carrierCode])

  @@unique([cityCode, carrierCode])
  @@index([cityCode])
  @@index([carrierCode])
}

// Service types offered by carriers (express, standard, etc.)
model CarrierService {
  id               String                    @id @default(uuid()) @db.Uuid
  carrierCode      String
  name             String
  pickupType       ServiceAvailabilityType[] @default([])
  dropoffType      ServiceAvailabilityType[] @default([])
  region           String?
  pickupTime       String? // e.g., 2-4 days
  transitTime      String? // e.g., 2-4 days
  deliveryTime     String? // e.g., 2-4 days
  deliveryDays     String[]                  @default(["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"])
  pickupDays       String[]                  @default(["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"])
  dimensionalLimit Json?
  createdAt        DateTime                  @default(now()) @db.Timestamptz(6)
  updatedAt        DateTime                  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  carrier Carrier @relation(fields: [carrierCode], references: [carrierCode], onDelete: NoAction, onUpdate: NoAction)

  @@index([carrierCode])
}

// ========================================================================
// --- ZONE & PRICING MODELS ---
// ========================================================================

// Shipping zones for carriers within countries
model Zone {
  id          String    @id @default(uuid()) @db.Uuid
  countryCode String    @db.Char(2)
  carrierCode String
  zoneCode    String
  zoneName    String
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  country    Country      @relation(fields: [countryCode], references: [code], onDelete: NoAction, onUpdate: NoAction)
  carrier    Carrier      @relation(fields: [carrierCode], references: [carrierCode])
  zoneCities ZoneToCity[]
  costsFrom  Cost[]       @relation("CostFromZone")
  costsTo    Cost[]       @relation("CostToZone")
  ratesFrom  Rate[]       @relation("RateFromZone")
  ratesTo    Rate[]       @relation("RateToZone")

  @@unique([countryCode, carrierCode, zoneCode])
  @@index([countryCode])
}

// Mapping between zones and cities
model ZoneToCity {
  zoneId String @db.Uuid
  cityId String @db.Uuid

  // --- Relationships ---
  zone Zone @relation(fields: [zoneId], references: [id], onDelete: Cascade)
  city City @relation(fields: [cityId], references: [id], onDelete: Cascade)

  @@id([zoneId, cityId])
}

// Base shipping costs for carriers between zones
model Cost {
  id              String    @id @default(uuid()) @db.Uuid
  fromCountryCode String    @db.Char(2)
  fromZone        String?
  toCountryCode   String    @db.Char(2)
  toZone          String?
  carrierCode     String
  fromWeight      Decimal   @db.Decimal(12, 3)
  toWeight        Decimal   @db.Decimal(12, 3)
  rateType        String
  costAmount      Decimal   @db.Decimal(12, 2)
  baseFee         Decimal?  @db.Decimal(12, 2)
  currency        String    @default("SAR")
  validFrom       DateTime? @db.Date
  validTo         DateTime? @db.Date
  createdAt       DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  carrier          Carrier @relation(fields: [carrierCode], references: [carrierCode], onDelete: NoAction, onUpdate: NoAction)
  fromCountry      Country @relation("CostFromCountry", fields: [fromCountryCode], references: [code], onDelete: NoAction, onUpdate: NoAction)
  toCountry        Country @relation("CostToCountry", fields: [toCountryCode], references: [code], onDelete: NoAction, onUpdate: NoAction)
  fromZoneRelation Zone?   @relation("CostFromZone", fields: [fromCountryCode, fromZone, carrierCode], references: [countryCode, zoneCode, carrierCode], onDelete: NoAction, onUpdate: NoAction)
  toZoneRelation   Zone?   @relation("CostToZone", fields: [toCountryCode, toZone, carrierCode], references: [countryCode, zoneCode, carrierCode], onDelete: NoAction, onUpdate: NoAction)

  @@index([fromCountryCode, toCountryCode, carrierCode, fromWeight, toWeight])
}

// Customer-facing shipping rates based on plans and costs
model Rate {
  id              String    @id @default(uuid()) @db.Uuid
  fromCountryCode String    @db.Char(2)
  fromZone        String?
  toCountryCode   String    @db.Char(2)
  toZone          String?
  carrierCode     String
  fromWeight      Decimal   @db.Decimal(12, 3)
  toWeight        Decimal   @db.Decimal(12, 3)
  rateType        String
  rateAmount      Decimal   @db.Decimal(12, 2)
  baseFee         Decimal?  @db.Decimal(12, 2)
  currency        String    @default("SAR")
  planId          String    @db.Uuid
  deliveryDays    Int?
  validFrom       DateTime? @db.Date
  validTo         DateTime? @db.Date
  createdAt       DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime? @updatedAt @db.Timestamptz(6)
  userId          String?   @db.Uuid
  organizationId  String?   @db.Uuid

  // --- Relationships ---
  carrier          Carrier       @relation(fields: [carrierCode], references: [carrierCode], onDelete: NoAction, onUpdate: NoAction)
  fromCountry      Country       @relation("RateFromCountry", fields: [fromCountryCode], references: [code], onDelete: NoAction, onUpdate: NoAction)
  organization     Organization? @relation(fields: [organizationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  plan             Plan          @relation(fields: [planId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  toCountry        Country       @relation("RateToCountry", fields: [toCountryCode], references: [code], onDelete: NoAction, onUpdate: NoAction)
  user             User?         @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  fromZoneRelation Zone?         @relation("RateFromZone", fields: [fromCountryCode, fromZone, carrierCode], references: [countryCode, zoneCode, carrierCode], onDelete: NoAction, onUpdate: NoAction)
  toZoneRelation   Zone?         @relation("RateToZone", fields: [toCountryCode, toZone, carrierCode], references: [countryCode, zoneCode, carrierCode], onDelete: NoAction, onUpdate: NoAction)

  @@index([fromCountryCode, toCountryCode, carrierCode, fromWeight, toWeight])
  @@index([organizationId])
  @@index([planId])
  @@index([userId])
}

// ========================================================================
// --- SUBSCRIPTION & BILLING MODELS ---
// ========================================================================

// Subscription plans with different features and pricing
model Plan {
  id               String   @id @default(uuid()) @db.Uuid
  name             String
  code             String   @unique
  description      String?
  price            Decimal  @db.Decimal(12, 2)
  durationInMonths Int?     @default(1)
  isActive         Boolean? @default(true)
  upgradeTo        String[] @db.Uuid
  trialPeriodDays  Int?
  createdAt        DateTime @default(now()) @db.Timestamptz(6)
  updatedAt        DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  coupons       Coupon[]
  organizations Organization[]
  planServices  PlanService[]
  rates         Rate[]
}

// Available services that can be included in plans
model Service {
  id          String   @id @default(uuid()) @db.Uuid
  code        String   @unique
  name        String
  description String?
  createdAt   DateTime @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  planServices         PlanService[]
  userServiceOverrides UserServiceOverride[]
}

// Mapping between plans and their included services
model PlanService {
  id        String   @id @default(uuid()) @db.Uuid
  planId    String?  @db.Uuid
  serviceId String?  @db.Uuid
  enabled   Boolean? @default(true)
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  plan    Plan?    @relation(fields: [planId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  service Service? @relation(fields: [serviceId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

// Service overrides for specific users
model UserServiceOverride {
  userId    String    @db.Uuid
  serviceId String    @db.Uuid
  isEnabled Boolean
  reason    String?
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  service Service @relation(fields: [serviceId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user    User    @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([userId, serviceId])
}

// Discount coupons for plans
model Coupon {
  id              String    @id @default(uuid()) @db.Uuid
  code            String    @unique
  discountType    String
  discountValue   Decimal   @db.Decimal(12, 2)
  usageLimit      Int?
  usedCount       Int?      @default(0)
  maxUsagePerUser Int?
  minOrderValue   Decimal?  @db.Decimal(12, 2)
  planId          String?   @db.Uuid
  expiresAt       DateTime? @db.Timestamptz(6)
  isActive        Boolean?  @default(true)
  createdAt       DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  couponUsages CouponUsage[]
  plan         Plan?         @relation(fields: [planId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([code])
  @@index([planId])
}

// Tracking coupon usage by users
model CouponUsage {
  id        String    @id @default(uuid()) @db.Uuid
  couponId  String?   @db.Uuid
  userId    String    @db.Uuid
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  coupon Coupon? @relation(fields: [couponId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user   User    @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([couponId])
  @@index([userId])
}

// ========================================================================
// --- WALLET & TRANSACTION MODELS ---
// ========================================================================

// User wallets for account balance management
model Wallet {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @unique @db.Uuid
  name      String
  balance   Decimal   @default(0.00) @db.Decimal(12, 2)
  currency  String?   @default("SAR")
  isActive  Boolean?  @default(true)
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  walletTransactions WalletTransaction[]
  user               User                @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([userId])
}

// Financial transactions in the system
model Transaction {
  id                   String    @id @default(uuid()) @db.Uuid
  userId               String    @db.Uuid
  amount               Decimal   @db.Decimal(12, 2)
  currency             String?   @default("SAR")
  status               String
  debitType            String?
  debitId              String?   @db.Uuid
  creditType           String?
  creditId             String?   @db.Uuid
  paymentGateway       String?
  gatewayTransactionId String?
  gatewayResponseCode  String?
  description          String?
  createdAt            DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt            DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user               User                @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  walletTransactions WalletTransaction[]

  @@index([createdAt])
  @@index([gatewayTransactionId])
  @@index([status])
  @@index([userId])
}

// Link between wallet and transaction with balance tracking
model WalletTransaction {
  id            String    @id @default(uuid()) @db.Uuid
  walletId      String    @db.Uuid
  transactionId String    @db.Uuid
  type          String
  amount        Decimal   @db.Decimal(12, 2)
  balanceBefore Decimal?  @db.Decimal(12, 2)
  balanceAfter  Decimal?  @db.Decimal(12, 2)
  createdAt     DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  wallet      Wallet      @relation(fields: [walletId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([transactionId])
  @@index([walletId])
}

// ========================================================================
// --- PRODUCT & INVENTORY MODELS ---
// ========================================================================

// Products that can be sold and shipped
model Product {
  id              String    @id @default(uuid()) @db.Uuid
  userId          String    @db.Uuid
  externalId      String?
  platformId      String?   @db.Uuid
  name            String
  sku             String?
  barcode         String?
  description     String?
  price           Decimal?  @db.Decimal(12, 2)
  currency        String?   @default("SAR")
  hsCode          String?
  countryOfOrigin String?   @default("CN") @db.Char(2)
  weightGrams     Decimal?  @db.Decimal(12, 3)
  lengthCm        Decimal?  @db.Decimal(12, 2)
  widthCm         Decimal?  @db.Decimal(12, 2)
  heightCm        Decimal?  @db.Decimal(12, 2)
  isActive        Boolean?  @default(true)
  syncedAt        DateTime? @db.Timestamptz(6)
  createdAt       DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime? @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  orderItems       OrderItem[]
  productImages    ProductImage[]
  productInventory ProductInventory[]
  productSyncLogs  ProductSyncLog[]
  productVariants  ProductVariant[]
  platform         Platform?          @relation(fields: [platformId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user             User?              @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([userId, sku])
  @@index([name])
  @@index([barcode])
  @@index([platformId])
  @@index([sku])
  @@index([userId])
}

// Product variations (size, color, etc.)
model ProductVariant {
  id          String    @id @default(uuid()) @db.Uuid
  productId   String?   @db.Uuid
  userId      String?   @db.Uuid
  name        String
  sku         String?
  barcode     String?
  price       Decimal?  @db.Decimal(12, 2)
  weightGrams Decimal?  @db.Decimal(12, 3)
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  productInventory ProductInventory[]
  product          Product?           @relation(fields: [productId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user             User?              @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([barcode])
  @@index([productId])
  @@index([sku])
}

// Product images for display
model ProductImage {
  id        String    @id @default(uuid()) @db.Uuid
  productId String?   @db.Uuid
  userId    String?   @db.Uuid
  imageUrl  String
  isPrimary Boolean?  @default(false)
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  product Product? @relation(fields: [productId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user    User?    @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

// ========================================================================
// --- WAREHOUSE & INVENTORY MODELS ---
// ========================================================================

// Warehouse locations for inventory storage
model Warehouse {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String?   @db.Uuid
  name      String
  code      String    @unique
  address1  String?
  address2  String?
  cityName  String?
  state     String?
  zip       String?
  country   String?
  cityId    String?   @db.Uuid
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)
  isActive  Boolean?

  // --- Relationships ---
  warehouseBins    WarehouseBin[]
  productInventory ProductInventory[]
  user             User?              @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  city             City?              @relation(fields: [cityId], references: [id])

  @@index([code])
  @@index([userId])
}

// Storage bins within warehouses
model WarehouseBin {
  id          String    @id @default(uuid()) @db.Uuid
  warehouseId String?   @db.Uuid
  userId      String?   @db.Uuid
  code        String
  zone        String?
  description String?
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  user             User?              @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  warehouse        Warehouse?         @relation(fields: [warehouseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  productInventory ProductInventory[]

  @@index([code])
  @@index([warehouseId])
}

// Inventory tracking for products in warehouses
model ProductInventory {
  id             String    @id @default(uuid()) @db.Uuid
  productId      String?   @db.Uuid
  variantId      String?   @db.Uuid
  warehouseId    String?   @db.Uuid
  binId          String?   @db.Uuid
  userId         String?   @db.Uuid
  batchNumber    String?
  expirationDate DateTime? @db.Date
  quantity       Int
  createdAt      DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  bin            WarehouseBin?   @relation(fields: [binId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product        Product?        @relation(fields: [productId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user           User?           @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  productVariant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  warehouse      Warehouse?      @relation(fields: [warehouseId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([productId])
  @@index([variantId])
  @@index([warehouseId])
}

// ========================================================================
// --- ORDER & SHIPMENT MODELS ---
// ========================================================================

// Main order entity containing shipment details
model Order {
  id              String         @id @default(uuid()) @db.Uuid
  orderNumber     String         @unique
  userId          String         @db.Uuid
  type            OrderType
  fromAddress     Json?
  toAddress       Json?
  platformOrderId String?        @db.Uuid
  status          OrderStatus    @default(PENDING)
  description     String?
  paymentStatus   PaymentStatus? @default(UNPAID)
  trackingNumber  String?
  declaredValue   Decimal?       @db.Decimal(12, 2)
  codAmount       Decimal?       @db.Decimal(12, 2)
  source          String?
  carrierCode     String?
  metadata        Json?
  addressId       String? // Optional field to link with address just for stats. No FK
  recipientPhone  String?
  linkToken       String?        @unique
  linkExpiresAt   DateTime?      @db.Timestamptz(6)
  createdAt       DateTime?      @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime?      @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  orderCharges  OrderCharge[]
  orderItems    OrderItem[]
  carrier       Carrier?       @relation(fields: [carrierCode], references: [carrierCode], onDelete: NoAction, onUpdate: NoAction)
  platformOrder PlatformOrder? @relation(fields: [platformOrderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user          User           @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  parcels       Parcel[]
  trackingData  TrackingData[]

  @@index([carrierCode])
  @@index([createdAt])
  @@index([platformOrderId])
  @@index([status])
  @@index([trackingNumber])
  @@index([userId])
  @@index([linkToken])
}

// Items within an order
model OrderItem {
  id          String    @id @default(uuid()) @db.Uuid
  userId      String    @db.Uuid
  orderId     String    @db.Uuid
  productId   String?   @db.Uuid
  productName String
  productSku  String?
  quantity    Int       @default(1)
  unitPrice   Decimal   @db.Decimal(12, 2)
  totalPrice  Decimal   @db.Decimal(12, 2)
  metadata    Json?
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order   Order    @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product Product? @relation(fields: [productId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user    User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([orderId])
  @@index([productId])
  @@index([userId])
}

// Additional charges on orders (shipping, handling, etc.)
model OrderCharge {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @db.Uuid
  orderId   String    @db.Uuid
  name      String
  type      String?
  amount    Decimal   @db.Decimal(12, 2)
  currency  String?   @default("SAR")
  metadata  Json?
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order Order @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user  User  @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

// Physical packages/parcels in a shipment
model Parcel {
  id                String    @id @default(uuid()) @db.Uuid
  userId            String    @db.Uuid
  orderId           String    @db.Uuid
  length            Decimal   @db.Decimal(12, 2)
  width             Decimal   @db.Decimal(12, 2)
  height            Decimal   @db.Decimal(12, 2)
  actualWeight      Decimal?  @db.Decimal(12, 3)
  dimensionalWeight Decimal?  @db.Decimal(12, 3)
  finalWeight       Decimal?  @db.Decimal(12, 3)
  trackingNumber    String?
  metadata          Json?
  createdAt         DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt         DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order Order @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user  User  @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([orderId])
  @@index([trackingNumber])
}

// Tracking updates for shipments
model TrackingData {
  id         String    @id @default(uuid()) @db.Uuid
  orderId    String?   @db.Uuid
  status     String?
  checkpoint String?
  createdAt  DateTime? @db.Timestamptz(6)
  updatedAt  DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  order Order? @relation(fields: [orderId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([orderId])
}

// ========================================================================
// --- E-COMMERCE PLATFORM INTEGRATION MODELS ---
// ========================================================================

// E-commerce platforms (Shopify, WooCommerce, etc.)
model Platform {
  id        String    @id @default(uuid()) @db.Uuid
  code      String    @unique
  name      String
  logoUrl   String?
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  platformIntegrations PlatformIntegration[]
  platformOrders       PlatformOrder[]
  productSyncLogs      ProductSyncLog[]
  products             Product[]
}

// User connections to e-commerce platforms
model PlatformIntegration {
  id             String    @id @default(uuid()) @db.Uuid
  userId         String?   @db.Uuid
  platformId     String?   @db.Uuid
  externalShopId String?
  accessToken    String?
  refreshToken   String?
  tokenExpiresAt DateTime? @db.Timestamptz(6)
  shopName       String?
  connectedAt    DateTime? @default(now()) @db.Timestamptz(6)
  isActive       Boolean?  @default(true)
  updatedAt      DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  platform Platform? @relation(fields: [platformId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user     User?     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([platformId])
  @@index([userId])
}

// Orders imported from e-commerce platforms
model PlatformOrder {
  id                String    @id @default(uuid()) @db.Uuid
  userId            String    @db.Uuid
  platformId        String    @db.Uuid
  externalOrderId   String
  status            String?
  customerName      String?
  customerAddressId String?   @db.Uuid
  items             Json
  rawPayload        Json?
  syncedAt          DateTime? @default(now()) @db.Timestamptz(6)
  createdAt         DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt         DateTime  @updatedAt @db.Timestamptz(6)

  // --- Relationships ---
  orders   Order[]
  address  Address? @relation(fields: [customerAddressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  platform Platform @relation(fields: [platformId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user     User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([externalOrderId])
  @@index([platformId])
  @@index([userId])
}

// Product synchronization logs from platforms
model ProductSyncLog {
  id           String    @id @default(uuid()) @db.Uuid
  productId    String?   @db.Uuid
  platformId   String?   @db.Uuid
  userId       String?   @db.Uuid
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime  @updatedAt @db.Timestamptz(6)
  syncStatus   String?
  errorMessage String?

  // --- Relationships ---
  platform Platform? @relation(fields: [platformId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  product  Product?  @relation(fields: [productId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user     User?     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

// ========================================================================
// --- SYSTEM & AUDIT MODELS ---
// ========================================================================

// Activity logs for user actions
model ActivityLog {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String?   @db.Uuid
  tableName String?
  recordId  String?   @db.Uuid
  action    String?
  message   String?
  level     String?
  data      Json?
  createdAt DateTime? @default(now()) @db.Timestamptz(6)

  // --- Relationships ---
  user User? @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([createdAt])
  @@index([userId])
}

// Audit trail for data changes
model AuditTrail {
  id           String    @id @default(uuid()) @db.Uuid
  userId       String?   @db.Uuid
  tableName    String?
  recordId     String?
  fieldChanged String?
  oldValue     String?
  newValue     String?
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)

  // --- Relationships ---
  user User? @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([createdAt])
  @@index([userId])
}

// Webhook logs for external integrations
model WebhookLog {
  id        String    @id @default(uuid()) @db.Uuid
  payload   Json?
  source    String?
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt DateTime  @updatedAt @db.Timestamptz(6)

  @@index([createdAt])
}

// Content translations for multi-language support
model ContentTranslation {
  id              String   @id @default(uuid()) @db.Uuid
  tableName       String
  recordId        String   @db.Uuid
  fieldName       String
  languageCode    String
  translatedValue String
  createdAt       DateTime @default(now()) @db.Timestamptz(6)
  updatedAt       DateTime @updatedAt @db.Timestamptz(6)

  @@index([tableName, recordId])
}
