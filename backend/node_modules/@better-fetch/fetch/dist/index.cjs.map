{"version": 3, "sources": ["../src/index.ts", "../src/error.ts", "../src/plugins.ts", "../src/retry.ts", "../src/auth.ts", "../src/utils.ts", "../src/create-fetch/schema.ts", "../src/create-fetch/index.ts", "../src/url.ts", "../src/fetch.ts"], "sourcesContent": ["export * from \"./fetch\";\nexport * from \"./types\";\nexport * from \"./create-fetch\";\nexport * from \"./error\";\nexport * from \"./utils\";\nexport * from \"./plugins\";\nexport * from \"./retry\";\nexport type * from \"./standard-schema\";\n", "export class BetterFetchError extends Error {\n\tconstructor(\n\t\tpublic status: number,\n\t\tpublic statusText: string,\n\t\tpublic error: any,\n\t) {\n\t\tsuper(statusText || status.toString(), {\n\t\t\tcause: error,\n\t\t});\n\t}\n}\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { Schema } from \"./create-fetch\";\nimport { BetterFetchError } from \"./error\";\nimport type { BetterFetchOption } from \"./types\";\n\nexport type RequestContext<T extends Record<string, any> = any> = {\n\turl: URL | string;\n\theaders: Headers;\n\tbody: any;\n\tmethod: string;\n\tsignal: AbortSignal;\n} & BetterFetchOption<any, any, any, T>;\nexport type ResponseContext = {\n\tresponse: Response;\n\trequest: RequestContext;\n};\nexport type SuccessContext<Res = any> = {\n\tdata: Res;\n\tresponse: Response;\n\trequest: RequestContext;\n};\nexport type ErrorContext = {\n\tresponse: Response;\n\trequest: RequestContext;\n\terror: BetterFetchError & Record<string, any>;\n};\nexport interface FetchHooks<Res = any> {\n\t/**\n\t * a callback function that will be called when a\n\t * request is made.\n\t *\n\t * The returned context object will be reassigned to\n\t * the original request context.\n\t */\n\tonRequest?: <T extends Record<string, any>>(\n\t\tcontext: RequestContext<T>,\n\t) => Promise<RequestContext | void> | RequestContext | void;\n\t/**\n\t * a callback function that will be called when\n\t * response is received. This will be called before\n\t * the response is parsed and returned.\n\t *\n\t * The returned response will be reassigned to the\n\t * original response if it's changed.\n\t */\n\tonResponse?: (\n\t\tcontext: ResponseContext,\n\t) =>\n\t\t| Promise<Response | void | ResponseContext>\n\t\t| Response\n\t\t| ResponseContext\n\t\t| void;\n\t/**\n\t * a callback function that will be called when a\n\t * response is successful.\n\t */\n\tonSuccess?: (context: SuccessContext<Res>) => Promise<void> | void;\n\t/**\n\t * a callback function that will be called when an\n\t * error occurs.\n\t */\n\tonError?: (context: ErrorContext) => Promise<void> | void;\n\t/**\n\t * a callback function that will be called when a\n\t * request is retried.\n\t */\n\tonRetry?: (response: ResponseContext) => Promise<void> | void;\n\t/**\n\t * Options for the hooks\n\t */\n\thookOptions?: {\n\t\t/**\n\t\t * Clone the response\n\t\t * @see https://developer.mozilla.org/en-US/docs/Web/API/Response/clone\n\t\t */\n\t\tcloneResponse?: boolean;\n\t};\n}\n\n/**\n * A plugin that returns an id and hooks\n */\nexport type BetterFetchPlugin = {\n\t/**\n\t * A unique id for the plugin\n\t */\n\tid: string;\n\t/**\n\t * A name for the plugin\n\t */\n\tname: string;\n\t/**\n\t * A description for the plugin\n\t */\n\tdescription?: string;\n\t/**\n\t * A version for the plugin\n\t */\n\tversion?: string;\n\t/**\n\t * Hooks for the plugin\n\t */\n\thooks?: FetchHooks;\n\t/**\n\t * A function that will be called when the plugin is\n\t * initialized. This will be called before the any\n\t * of the other internal functions.\n\t *\n\t * The returned options will be merged with the\n\t * original options.\n\t */\n\tinit?: (\n\t\turl: string,\n\t\toptions?: BetterFetchOption,\n\t) =>\n\t\t| Promise<{\n\t\t\t\turl: string;\n\t\t\t\toptions?: BetterFetchOption;\n\t\t  }>\n\t\t| {\n\t\t\t\turl: string;\n\t\t\t\toptions?: BetterFetchOption;\n\t\t  };\n\t/**\n\t * A schema for the plugin\n\t */\n\tschema?: Schema;\n\t/**\n\t * Additional options that can be passed to the plugin\n\t */\n\tgetOptions?: () => StandardSchemaV1;\n};\n\nexport const initializePlugins = async (\n\turl: string,\n\toptions?: BetterFetchOption,\n) => {\n\tlet opts = options || {};\n\tconst hooks: {\n\t\tonRequest: Array<FetchHooks[\"onRequest\"]>;\n\t\tonResponse: Array<FetchHooks[\"onResponse\"]>;\n\t\tonSuccess: Array<FetchHooks[\"onSuccess\"]>;\n\t\tonError: Array<FetchHooks[\"onError\"]>;\n\t\tonRetry: Array<FetchHooks[\"onRetry\"]>;\n\t} = {\n\t\tonRequest: [options?.onRequest],\n\t\tonResponse: [options?.onResponse],\n\t\tonSuccess: [options?.onSuccess],\n\t\tonError: [options?.onError],\n\t\tonRetry: [options?.onRetry],\n\t};\n\tif (!options || !options?.plugins) {\n\t\treturn {\n\t\t\turl,\n\t\t\toptions: opts,\n\t\t\thooks,\n\t\t};\n\t}\n\tfor (const plugin of options?.plugins || []) {\n\t\tif (plugin.init) {\n\t\t\tconst pluginRes = await plugin.init?.(url.toString(), options);\n\t\t\topts = pluginRes.options || opts;\n\t\t\turl = pluginRes.url;\n\t\t}\n\t\thooks.onRequest.push(plugin.hooks?.onRequest);\n\t\thooks.onResponse.push(plugin.hooks?.onResponse);\n\t\thooks.onSuccess.push(plugin.hooks?.onSuccess);\n\t\thooks.onError.push(plugin.hooks?.onError);\n\t\thooks.onRetry.push(plugin.hooks?.onRetry);\n\t}\n\n\treturn {\n\t\turl,\n\t\toptions: opts,\n\t\thooks,\n\t};\n};\n", "export type RetryCondition = (\n\tresponse: Response | null,\n) => boolean | Promise<boolean>;\n\nexport type LinearRetry = {\n\ttype: \"linear\";\n\tattempts: number;\n\tdelay: number;\n\tshouldRetry?: RetryCondition;\n};\n\nexport type ExponentialRetry = {\n\ttype: \"exponential\";\n\tattempts: number;\n\tbaseDelay: number;\n\tmaxDelay: number;\n\tshouldRetry?: RetryCondition;\n};\n\nexport type RetryOptions = LinearRetry | ExponentialRetry | number;\n\nexport interface RetryStrategy {\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean>;\n\tgetDelay(attempt: number): number;\n}\n\nclass LinearRetryStrategy implements RetryStrategy {\n\tconstructor(private options: LinearRetry) {}\n\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean> {\n\t\tif (this.options.shouldRetry) {\n\t\t\treturn Promise.resolve(\n\t\t\t\tattempt < this.options.attempts && this.options.shouldRetry(response),\n\t\t\t);\n\t\t}\n\t\treturn Promise.resolve(attempt < this.options.attempts);\n\t}\n\n\tgetDelay(): number {\n\t\treturn this.options.delay;\n\t}\n}\n\nclass ExponentialRetryStrategy implements RetryStrategy {\n\tconstructor(private options: ExponentialRetry) {}\n\n\tshouldAttemptRetry(\n\t\tattempt: number,\n\t\tresponse: Response | null,\n\t): Promise<boolean> {\n\t\tif (this.options.shouldRetry) {\n\t\t\treturn Promise.resolve(\n\t\t\t\tattempt < this.options.attempts && this.options.shouldRetry(response),\n\t\t\t);\n\t\t}\n\t\treturn Promise.resolve(attempt < this.options.attempts);\n\t}\n\n\tgetDelay(attempt: number): number {\n\t\tconst delay = Math.min(\n\t\t\tthis.options.maxDelay,\n\t\t\tthis.options.baseDelay * 2 ** attempt,\n\t\t);\n\t\treturn delay;\n\t}\n}\n\nexport function createRetryStrategy(options: RetryOptions): RetryStrategy {\n\tif (typeof options === \"number\") {\n\t\treturn new LinearRetryStrategy({\n\t\t\ttype: \"linear\",\n\t\t\tattempts: options,\n\t\t\tdelay: 1000,\n\t\t});\n\t}\n\n\tswitch (options.type) {\n\t\tcase \"linear\":\n\t\t\treturn new LinearRetryStrategy(options);\n\t\tcase \"exponential\":\n\t\t\treturn new ExponentialRetryStrategy(options);\n\t\tdefault:\n\t\t\tthrow new Error(\"Invalid retry strategy\");\n\t}\n}\n", "import type { BetterFetchOption } from \"./types\";\n\nexport type typeOrTypeReturning<T> = T | (() => T);\n/**\n * Bearer token authentication\n *\n * the value of `token` will be added to a header as\n * `auth: Bearer token`,\n */\nexport type Bearer = {\n\ttype: \"Bearer\";\n\ttoken: typeOrTypeReturning<string | undefined | Promise<string | undefined>>;\n};\n\n/**\n * Basic auth\n */\nexport type Basic = {\n\ttype: \"Basic\";\n\tusername: typeOrTypeReturning<string | undefined>;\n\tpassword: typeOrTypeReturning<string | undefined>;\n};\n\n/**\n * Custom auth\n *\n * @param prefix - prefix of the header\n * @param value - value of the header\n *\n * @example\n * ```ts\n * {\n *  type: \"Custom\",\n *  prefix: \"Token\",\n *  value: \"token\"\n * }\n * ```\n */\nexport type Custom = {\n\ttype: \"Custom\";\n\tprefix: typeOrTypeReturning<string | undefined>;\n\tvalue: typeOrTypeReturning<string | undefined>;\n};\n\nexport type Auth = Bearer | Basic | Custom;\n\nexport const getAuthHeader = async (options?: BetterFetchOption) => {\n\tconst headers: Record<string, string> = {};\n\tconst getValue = async (\n\t\tvalue: typeOrTypeReturning<\n\t\t\tstring | undefined | Promise<string | undefined>\n\t\t>,\n\t) => (typeof value === \"function\" ? await value() : value);\n\tif (options?.auth) {\n\t\tif (options.auth.type === \"Bearer\") {\n\t\t\tconst token = await getValue(options.auth.token);\n\t\t\tif (!token) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `Bearer ${token}`;\n\t\t} else if (options.auth.type === \"Basic\") {\n\t\t\tconst username = getValue(options.auth.username);\n\t\t\tconst password = getValue(options.auth.password);\n\t\t\tif (!username || !password) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `Basic ${btoa(`${username}:${password}`)}`;\n\t\t} else if (options.auth.type === \"Custom\") {\n\t\t\tconst value = getValue(options.auth.value);\n\t\t\tif (!value) {\n\t\t\t\treturn headers;\n\t\t\t}\n\t\t\theaders[\"authorization\"] = `${getValue(options.auth.prefix)} ${value}`;\n\t\t}\n\t}\n\treturn headers;\n};\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { getAuthHeader } from \"./auth\";\nimport { methods } from \"./create-fetch\";\nimport type { BetterFetchOption, FetchEsque } from \"./types\";\n\nconst JSON_RE = /^application\\/(?:[\\w!#$%&*.^`~-]*\\+)?json(;.+)?$/i;\n\nexport type ResponseType = \"json\" | \"text\" | \"blob\";\nexport function detectResponseType(request: Response): ResponseType {\n\tconst _contentType = request.headers.get(\"content-type\");\n\tconst textTypes = new Set([\n\t\t\"image/svg\",\n\t\t\"application/xml\",\n\t\t\"application/xhtml\",\n\t\t\"application/html\",\n\t]);\n\tif (!_contentType) {\n\t\treturn \"json\";\n\t}\n\tconst contentType = _contentType.split(\";\").shift() || \"\";\n\tif (JSON_RE.test(contentType)) {\n\t\treturn \"json\";\n\t}\n\tif (textTypes.has(contentType) || contentType.startsWith(\"text/\")) {\n\t\treturn \"text\";\n\t}\n\treturn \"blob\";\n}\n\nexport function isJSONParsable(value: any) {\n\ttry {\n\t\tJSON.parse(value);\n\t\treturn true;\n\t} catch (error) {\n\t\treturn false;\n\t}\n}\n\n//https://github.com/unjs/ofetch/blob/main/src/utils.ts\nexport function isJSONSerializable(value: any) {\n\tif (value === undefined) {\n\t\treturn false;\n\t}\n\tconst t = typeof value;\n\tif (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n\t\treturn true;\n\t}\n\tif (t !== \"object\") {\n\t\treturn false;\n\t}\n\tif (Array.isArray(value)) {\n\t\treturn true;\n\t}\n\tif (value.buffer) {\n\t\treturn false;\n\t}\n\treturn (\n\t\t(value.constructor && value.constructor.name === \"Object\") ||\n\t\ttypeof value.toJSON === \"function\"\n\t);\n}\n\nexport function jsonParse(text: string) {\n\ttry {\n\t\treturn JSON.parse(text);\n\t} catch (error) {\n\t\treturn text;\n\t}\n}\n\nexport function isFunction(value: any): value is () => any {\n\treturn typeof value === \"function\";\n}\n\nexport function getFetch(options?: BetterFetchOption): FetchEsque {\n\tif (options?.customFetchImpl) {\n\t\treturn options.customFetchImpl;\n\t}\n\tif (typeof globalThis !== \"undefined\" && isFunction(globalThis.fetch)) {\n\t\treturn globalThis.fetch;\n\t}\n\tif (typeof window !== \"undefined\" && isFunction(window.fetch)) {\n\t\treturn window.fetch;\n\t}\n\tthrow new Error(\"No fetch implementation found\");\n}\n\nexport function isPayloadMethod(method?: string) {\n\tif (!method) {\n\t\treturn false;\n\t}\n\tconst payloadMethod = [\"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n\treturn payloadMethod.includes(method.toUpperCase());\n}\n\nexport function isRouteMethod(method?: string) {\n\tconst routeMethod = [\"GET\", \"POST\", \"PUT\", \"PATCH\", \"DELETE\"];\n\tif (!method) {\n\t\treturn false;\n\t}\n\treturn routeMethod.includes(method.toUpperCase());\n}\n\nexport async function getHeaders(opts?: BetterFetchOption) {\n\tconst headers = new Headers(opts?.headers);\n\tconst authHeader = await getAuthHeader(opts);\n\tfor (const [key, value] of Object.entries(authHeader || {})) {\n\t\theaders.set(key, value);\n\t}\n\tif (!headers.has(\"content-type\")) {\n\t\tconst t = detectContentType(opts?.body);\n\t\tif (t) {\n\t\t\theaders.set(\"content-type\", t);\n\t\t}\n\t}\n\n\treturn headers;\n}\n\nexport function getURL(url: string, options?: BetterFetchOption) {\n\tif (url.startsWith(\"@\")) {\n\t\tconst m = url.toString().split(\"@\")[1].split(\"/\")[0];\n\t\tif (methods.includes(m)) {\n\t\t\turl = url.replace(`@${m}/`, \"/\");\n\t\t}\n\t}\n\tlet _url: string | URL;\n\ttry {\n\t\tif (url.startsWith(\"http\")) {\n\t\t\t_url = url;\n\t\t} else {\n\t\t\tlet baseURL = options?.baseURL;\n\t\t\tif (baseURL && !baseURL?.endsWith(\"/\")) {\n\t\t\t\tbaseURL = baseURL + \"/\";\n\t\t\t}\n\t\t\tif (url.startsWith(\"/\")) {\n\t\t\t\t_url = new URL(url.substring(1), baseURL);\n\t\t\t} else {\n\t\t\t\t_url = new URL(url, options?.baseURL);\n\t\t\t}\n\t\t}\n\t} catch (e) {\n\t\tif (e instanceof TypeError) {\n\t\t\tif (!options?.baseURL) {\n\t\t\t\tthrow TypeError(\n\t\t\t\t\t`Invalid URL ${url}. Are you passing in a relative url but not setting the baseURL?`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tthrow TypeError(\n\t\t\t\t`Invalid URL ${url}. Please validate that you are passing the correct input.`,\n\t\t\t);\n\t\t}\n\t\tthrow e;\n\t}\n\n\t/**\n\t * Dynamic Parameters.\n\t */\n\tif (options?.params) {\n\t\tif (Array.isArray(options?.params)) {\n\t\t\tconst params = options?.params\n\t\t\t\t? Array.isArray(options.params)\n\t\t\t\t\t? `/${options.params.join(\"/\")}`\n\t\t\t\t\t: `/${Object.values(options.params).join(\"/\")}`\n\t\t\t\t: \"\";\n\t\t\t_url = _url.toString().split(\"/:\")[0];\n\t\t\t_url = `${_url.toString()}${params}`;\n\t\t} else {\n\t\t\tfor (const [key, value] of Object.entries(options?.params)) {\n\t\t\t\t_url = _url.toString().replace(`:${key}`, String(value));\n\t\t\t}\n\t\t}\n\t}\n\tconst __url = new URL(_url);\n\t/**\n\t * Query Parameters\n\t */\n\tconst queryParams = options?.query;\n\tif (queryParams) {\n\t\tfor (const [key, value] of Object.entries(queryParams)) {\n\t\t\t__url.searchParams.append(key, String(value));\n\t\t}\n\t}\n\treturn __url;\n}\n\nexport function detectContentType(body: any) {\n\tif (isJSONSerializable(body)) {\n\t\treturn \"application/json\";\n\t}\n\n\treturn null;\n}\n\nexport function getBody(options?: BetterFetchOption) {\n\tif (!options?.body) {\n\t\treturn null;\n\t}\n\tconst headers = new Headers(options?.headers);\n\tif (isJSONSerializable(options.body) && !headers.has(\"content-type\")) {\n\t\tfor (const [key, value] of Object.entries(options?.body)) {\n\t\t\tif (value instanceof Date) {\n\t\t\t\toptions.body[key] = value.toISOString();\n\t\t\t}\n\t\t}\n\t\treturn JSON.stringify(options.body);\n\t}\n\n\treturn options.body;\n}\n\nexport function getMethod(url: string, options?: BetterFetchOption) {\n\tif (options?.method) {\n\t\treturn options.method.toUpperCase();\n\t}\n\tif (url.startsWith(\"@\")) {\n\t\tconst pMethod = url.split(\"@\")[1]?.split(\"/\")[0];\n\t\tif (!methods.includes(pMethod)) {\n\t\t\treturn options?.body ? \"POST\" : \"GET\";\n\t\t}\n\t\treturn pMethod.toUpperCase();\n\t}\n\treturn options?.body ? \"POST\" : \"GET\";\n}\n\nexport function getTimeout(\n\toptions?: BetterFetchOption,\n\tcontroller?: AbortController,\n) {\n\tlet abortTimeout: ReturnType<typeof setTimeout> | undefined;\n\tif (!options?.signal && options?.timeout) {\n\t\tabortTimeout = setTimeout(() => controller?.abort(), options?.timeout);\n\t}\n\treturn {\n\t\tabortTimeout,\n\t\tclearTimeout: () => {\n\t\t\tif (abortTimeout) {\n\t\t\t\tclearTimeout(abortTimeout);\n\t\t\t}\n\t\t},\n\t};\n}\n\nexport function bodyParser(data: any, responseType: ResponseType) {\n\tif (responseType === \"json\") {\n\t\treturn JSON.parse(data);\n\t}\n\treturn data;\n}\n\nexport class ValidationError extends Error {\n\tpublic readonly issues: ReadonlyArray<StandardSchemaV1.Issue>;\n\n\tconstructor(issues: ReadonlyArray<StandardSchemaV1.Issue>, message?: string) {\n\t\t// Default message fallback in case one isn't supplied.\n\t\tsuper(message || JSON.stringify(issues, null, 2));\n\t\tthis.issues = issues;\n\n\t\t// Set the prototype explicitly to ensure that instanceof works correctly.\n\t\tObject.setPrototypeOf(this, ValidationError.prototype);\n\t}\n}\n\nexport async function parseStandardSchema<TSchema extends StandardSchemaV1>(\n\tschema: TSchema,\n\tinput: StandardSchemaV1.InferInput<TSchema>,\n): Promise<StandardSchemaV1.InferOutput<TSchema>> {\n\tlet result = await schema[\"~standard\"].validate(input);\n\n\tif (result.issues) {\n\t\tthrow new ValidationError(result.issues);\n\t}\n\treturn result.value;\n}\n", "import type { StandardSchemaV1 } from \"../standard-schema\";\nimport type { StringLiteralUnion } from \"../type-utils\";\n\nexport type FetchSchema = {\n\tinput?: StandardSchemaV1;\n\toutput?: StandardSchemaV1;\n\tquery?: StandardSchemaV1;\n\tparams?: StandardSchemaV1<Record<string, unknown>> | undefined;\n\tmethod?: Methods;\n};\n\nexport type Methods = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\n\nexport const methods = [\"get\", \"post\", \"put\", \"patch\", \"delete\"];\n\ntype RouteKey = StringLiteralUnion<`@${Methods}/`>;\n\nexport type FetchSchemaRoutes = {\n\t[key in RouteKey]?: FetchSchema;\n};\n\nexport const createSchema = <\n\tF extends FetchSchemaRoutes,\n\tS extends SchemaConfig,\n>(\n\tschema: F,\n\tconfig?: S,\n) => {\n\treturn {\n\t\tschema: schema as F,\n\t\tconfig: config as S,\n\t};\n};\n\nexport type SchemaConfig = {\n\tstrict?: boolean;\n\t/**\n\t * A prefix that will be prepended when it's\n\t * calling the schema.\n\t *\n\t * NOTE: Make sure to handle converting\n\t * the prefix to the baseURL in the init\n\t * function if you you are defining for a\n\t * plugin.\n\t */\n\tprefix?: \"\" | (string & Record<never, never>);\n\t/**\n\t * The base url of the schema. By default it's the baseURL of the fetch instance.\n\t */\n\tbaseURL?: \"\" | (string & Record<never, never>);\n};\n\nexport type Schema = {\n\tschema: FetchSchemaRoutes;\n\tconfig: SchemaConfig;\n};\n", "import { betterFetch } from \"../fetch\";\nimport { BetterFetchPlugin } from \"../plugins\";\nimport type { BetterFetchOption } from \"../types\";\nimport { parseStandardSchema } from \"../utils\";\nimport type { BetterFetch, CreateFetchOption } from \"./types\";\n\nexport const applySchemaPlugin = (config: CreateFetchOption) =>\n\t({\n\t\tid: \"apply-schema\",\n\t\tname: \"Apply Schema\",\n\t\tversion: \"1.0.0\",\n\t\tasync init(url, options) {\n\t\t\tconst schema =\n\t\t\t\tconfig.plugins?.find((plugin) =>\n\t\t\t\t\tplugin.schema?.config\n\t\t\t\t\t\t? url.startsWith(plugin.schema.config.baseURL || \"\") ||\n\t\t\t\t\t\t\turl.startsWith(plugin.schema.config.prefix || \"\")\n\t\t\t\t\t\t: false,\n\t\t\t\t)?.schema || config.schema;\n\t\t\tif (schema) {\n\t\t\t\tlet urlKey = url;\n\t\t\t\tif (schema.config?.prefix) {\n\t\t\t\t\tif (urlKey.startsWith(schema.config.prefix)) {\n\t\t\t\t\t\turlKey = urlKey.replace(schema.config.prefix, \"\");\n\t\t\t\t\t\tif (schema.config.baseURL) {\n\t\t\t\t\t\t\turl = url.replace(schema.config.prefix, schema.config.baseURL);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (schema.config?.baseURL) {\n\t\t\t\t\tif (urlKey.startsWith(schema.config.baseURL)) {\n\t\t\t\t\t\turlKey = urlKey.replace(schema.config.baseURL, \"\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst keySchema = schema.schema[urlKey];\n\t\t\t\tif (keySchema) {\n\t\t\t\t\tlet opts = {\n\t\t\t\t\t\t...options,\n\t\t\t\t\t\tmethod: keySchema.method,\n\t\t\t\t\t\toutput: keySchema.output,\n\t\t\t\t\t};\n\t\t\t\t\tif (!options?.disableValidation) {\n\t\t\t\t\t\topts = {\n\t\t\t\t\t\t\t...opts,\n\t\t\t\t\t\t\tbody: keySchema.input\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.input, options?.body)\n\t\t\t\t\t\t\t\t: options?.body,\n\t\t\t\t\t\t\tparams: keySchema.params\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.params, options?.params)\n\t\t\t\t\t\t\t\t: options?.params,\n\t\t\t\t\t\t\tquery: keySchema.query\n\t\t\t\t\t\t\t\t? await parseStandardSchema(keySchema.query, options?.query)\n\t\t\t\t\t\t\t\t: options?.query,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\treturn {\n\t\t\t\t\t\turl,\n\t\t\t\t\t\toptions: opts,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn {\n\t\t\t\turl,\n\t\t\t\toptions,\n\t\t\t};\n\t\t},\n\t}) satisfies BetterFetchPlugin;\n\nexport const createFetch = <Option extends CreateFetchOption>(\n\tconfig?: Option,\n) => {\n\tasync function $fetch(url: string, options?: BetterFetchOption) {\n\t\tconst opts = {\n\t\t\t...config,\n\t\t\t...options,\n\t\t\tplugins: [...(config?.plugins || []), applySchemaPlugin(config || {})],\n\t\t} as BetterFetchOption;\n\n\t\tif (config?.catchAllError) {\n\t\t\ttry {\n\t\t\t\treturn await betterFetch(url, opts);\n\t\t\t} catch (error) {\n\t\t\t\treturn {\n\t\t\t\t\tdata: null,\n\t\t\t\t\terror: {\n\t\t\t\t\t\tstatus: 500,\n\t\t\t\t\t\tstatusText: \"Fetch Error\",\n\t\t\t\t\t\tmessage:\n\t\t\t\t\t\t\t\"Fetch related error. Captured by catchAllError option. See error property for more details.\",\n\t\t\t\t\t\terror,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\treturn await betterFetch(url, opts);\n\t}\n\treturn $fetch as BetterFetch<Option>;\n};\n\nexport * from \"./schema\";\nexport * from \"./types\";\n", "import { methods } from \"./create-fetch\";\nimport { BetterFetchOption } from \"./types\";\n\n/**\n * Normalize URL\n */\nexport function getURL(url: string, option?: BetterFetchOption) {\n\tlet { baseURL, params, query } = option || {\n\t\tquery: {},\n\t\tparams: {},\n\t\tbaseURL: \"\",\n\t};\n\tlet basePath = url.startsWith(\"http\")\n\t\t? url.split(\"/\").slice(0, 3).join(\"/\")\n\t\t: baseURL || \"\";\n\n\t/**\n\t * Remove method modifiers\n\t */\n\tif (url.startsWith(\"@\")) {\n\t\tconst m = url.toString().split(\"@\")[1].split(\"/\")[0];\n\t\tif (methods.includes(m)) {\n\t\t\turl = url.replace(`@${m}/`, \"/\");\n\t\t}\n\t}\n\n\tif (!basePath.endsWith(\"/\")) basePath += \"/\";\n\tlet [path, urlQuery] = url.replace(basePath, \"\").split(\"?\");\n\tconst queryParams = new URLSearchParams(urlQuery);\n\tfor (const [key, value] of Object.entries(query || {})) {\n\t\tif (value == null) continue;\n\t\tqueryParams.set(key, String(value));\n\t}\n\tif (params) {\n\t\tif (Array.isArray(params)) {\n\t\t\tconst paramPaths = path.split(\"/\").filter((p) => p.startsWith(\":\"));\n\t\t\tfor (const [index, key] of paramPaths.entries()) {\n\t\t\t\tconst value = params[index];\n\t\t\t\tpath = path.replace(key, value);\n\t\t\t}\n\t\t} else {\n\t\t\tfor (const [key, value] of Object.entries(params)) {\n\t\t\t\tpath = path.replace(`:${key}`, String(value));\n\t\t\t}\n\t\t}\n\t}\n\n\tpath = path.split(\"/\").map(encodeURIComponent).join(\"/\");\n\tif (path.startsWith(\"/\")) path = path.slice(1);\n\tlet queryParamString = queryParams.toString();\n\tqueryParamString =\n\t\tqueryParamString.length > 0 ? `?${queryParamString}`.replace(/\\+/g, \"%20\") : \"\";\n\tif (!basePath.startsWith(\"http\")) {\n\t\treturn `${basePath}${path}${queryParamString}`;\n\t}\n\tconst _url = new URL(`${path}${queryParamString}`, basePath);\n\treturn _url;\n}\n", "import type { StandardSchemaV1 } from \"./standard-schema\";\nimport { BetterFetchError } from \"./error\";\nimport { initializePlugins } from \"./plugins\";\nimport { createRetryStrategy } from \"./retry\";\nimport type { BetterFetchOption, BetterFetchResponse } from \"./types\";\nimport { getURL } from \"./url\";\nimport {\n\tdetectResponseType,\n\tgetBody,\n\tgetFetch,\n\tgetHeaders,\n\tgetMethod,\n\tgetTimeout,\n\tisJSONParsable,\n\tjsonParse,\n\tparseStandardSchema,\n} from \"./utils\";\n\nexport const betterFetch = async <\n\tTRes extends Option[\"output\"] extends StandardSchemaV1\n\t\t? StandardSchemaV1.InferOutput<Option[\"output\"]>\n\t\t: unknown,\n\tTErr = unknown,\n\tOption extends BetterFetchOption = BetterFetchOption<any, any, any, TRes>,\n>(\n\turl: string,\n\toptions?: Option,\n): Promise<\n\tBetterFetchResponse<\n\t\tTRes,\n\t\tTErr,\n\t\tOption[\"throw\"] extends true ? true : TErr extends false ? true : false\n\t>\n> => {\n\tconst {\n\t\thooks,\n\t\turl: __url,\n\t\toptions: opts,\n\t} = await initializePlugins(url, options);\n\tconst fetch = getFetch(opts);\n\tconst controller = new AbortController();\n\tconst signal = opts.signal ?? controller.signal;\n\tconst _url = getURL(__url, opts);\n\tconst body = getBody(opts);\n\tconst headers = await getHeaders(opts);\n\tconst method = getMethod(__url, opts);\n\tlet context = {\n\t\t...opts,\n\t\turl: _url,\n\t\theaders,\n\t\tbody,\n\t\tmethod,\n\t\tsignal,\n\t};\n\t/**\n\t * Run all on request hooks\n\t */\n\tfor (const onRequest of hooks.onRequest) {\n\t\tif (onRequest) {\n\t\t\tconst res = await onRequest(context);\n\t\t\tif (res instanceof Object) {\n\t\t\t\tcontext = res;\n\t\t\t}\n\t\t}\n\t}\n\tif (\n\t\t(\"pipeTo\" in (context as any) &&\n\t\t\ttypeof (context as any).pipeTo === \"function\") ||\n\t\ttypeof options?.body?.pipe === \"function\"\n\t) {\n\t\tif (!(\"duplex\" in context)) {\n\t\t\tcontext.duplex = \"half\";\n\t\t}\n\t}\n\n\tconst { clearTimeout } = getTimeout(opts, controller);\n\tlet response = await fetch(context.url, context);\n\tclearTimeout();\n\n\tconst responseContext = {\n\t\tresponse,\n\t\trequest: context,\n\t};\n\n\tfor (const onResponse of hooks.onResponse) {\n\t\tif (onResponse) {\n\t\t\tconst r = await onResponse({\n\t\t\t\t...responseContext,\n\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t? response.clone()\n\t\t\t\t\t: response,\n\t\t\t});\n\t\t\tif (r instanceof Response) {\n\t\t\t\tresponse = r;\n\t\t\t} else if (r instanceof Object) {\n\t\t\t\tresponse = r.response;\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * OK Branch\n\t */\n\tif (response.ok) {\n\t\tconst hasBody = context.method !== \"HEAD\";\n\t\tif (!hasBody) {\n\t\t\treturn {\n\t\t\t\tdata: \"\" as any,\n\t\t\t\terror: null,\n\t\t\t} as any;\n\t\t}\n\t\tconst responseType = detectResponseType(response);\n\t\tconst successContext = {\n\t\t\tdata: \"\" as any,\n\t\t\tresponse,\n\t\t\trequest: context,\n\t\t};\n\t\tif (responseType === \"json\" || responseType === \"text\") {\n\t\t\tconst text = await response.text();\n\t\t\tconst parser = context.jsonParser ?? jsonParse;\n\t\t\tconst data = await parser(text);\n\t\t\tsuccessContext.data = data;\n\t\t} else {\n\t\t\tsuccessContext.data = await response[responseType]();\n\t\t}\n\n\t\t/**\n\t\t * Parse the data if the output schema is defined\n\t\t */\n\t\tif (context?.output) {\n\t\t\tif (context.output && !context.disableValidation) {\n\t\t\t\tsuccessContext.data = await parseStandardSchema(\n\t\t\t\t\tcontext.output as StandardSchemaV1,\n\t\t\t\t\tsuccessContext.data,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tfor (const onSuccess of hooks.onSuccess) {\n\t\t\tif (onSuccess) {\n\t\t\t\tawait onSuccess({\n\t\t\t\t\t...successContext,\n\t\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t\t? response.clone()\n\t\t\t\t\t\t: response,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (options?.throw) {\n\t\t\treturn successContext.data as any;\n\t\t}\n\n\t\treturn {\n\t\t\tdata: successContext.data,\n\t\t\terror: null,\n\t\t} as any;\n\t}\n\tconst parser = options?.jsonParser ?? jsonParse;\n\tconst responseText = await response.text();\n\tconst isJSONResponse = isJSONParsable(responseText);\n\tconst errorObject = isJSONResponse ? await parser(responseText) : null;\n\t/**\n\t * Error Branch\n\t */\n\tconst errorContext = {\n\t\tresponse,\n\t\tresponseText,\n\t\trequest: context,\n\t\terror: {\n\t\t\t...errorObject,\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t},\n\t};\n\tfor (const onError of hooks.onError) {\n\t\tif (onError) {\n\t\t\tawait onError({\n\t\t\t\t...errorContext,\n\t\t\t\tresponse: options?.hookOptions?.cloneResponse\n\t\t\t\t\t? response.clone()\n\t\t\t\t\t: response,\n\t\t\t});\n\t\t}\n\t}\n\n\tif (options?.retry) {\n\t\tconst retryStrategy = createRetryStrategy(options.retry);\n\t\tconst _retryAttempt = options.retryAttempt ?? 0;\n\t\tif (await retryStrategy.shouldAttemptRetry(_retryAttempt, response)) {\n\t\t\tfor (const onRetry of hooks.onRetry) {\n\t\t\t\tif (onRetry) {\n\t\t\t\t\tawait onRetry(responseContext);\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst delay = retryStrategy.getDelay(_retryAttempt);\n\t\t\tawait new Promise((resolve) => setTimeout(resolve, delay));\n\t\t\treturn await betterFetch(url, {\n\t\t\t\t...options,\n\t\t\t\tretryAttempt: _retryAttempt + 1,\n\t\t\t});\n\t\t}\n\t}\n\n\tif (options?.throw) {\n\t\tthrow new BetterFetchError(\n\t\t\tresponse.status,\n\t\t\tresponse.statusText,\n\t\t\tisJSONResponse ? errorObject : responseText,\n\t\t);\n\t}\n\treturn {\n\t\tdata: null,\n\t\terror: {\n\t\t\t...errorObject,\n\t\t\tstatus: response.status,\n\t\t\tstatusText: response.statusText,\n\t\t},\n\t} as any;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAM,mBAAN,cAA+B,MAAM;AAAA,EAC3C,YACQ,QACA,YACA,OACN;AACD,UAAM,cAAc,OAAO,SAAS,GAAG;AAAA,MACtC,OAAO;AAAA,IACR,CAAC;AANM;AACA;AACA;AAAA,EAKR;AACD;;;AC2HO,IAAM,oBAAoB,OAChC,KACA,YACI;AAxIL;AAyIC,MAAI,OAAO,WAAW,CAAC;AACvB,QAAM,QAMF;AAAA,IACH,WAAW,CAAC,mCAAS,SAAS;AAAA,IAC9B,YAAY,CAAC,mCAAS,UAAU;AAAA,IAChC,WAAW,CAAC,mCAAS,SAAS;AAAA,IAC9B,SAAS,CAAC,mCAAS,OAAO;AAAA,IAC1B,SAAS,CAAC,mCAAS,OAAO;AAAA,EAC3B;AACA,MAAI,CAAC,WAAW,EAAC,mCAAS,UAAS;AAClC,WAAO;AAAA,MACN;AAAA,MACA,SAAS;AAAA,MACT;AAAA,IACD;AAAA,EACD;AACA,aAAW,WAAU,mCAAS,YAAW,CAAC,GAAG;AAC5C,QAAI,OAAO,MAAM;AAChB,YAAM,YAAY,QAAM,YAAO,SAAP,gCAAc,IAAI,SAAS,GAAG;AACtD,aAAO,UAAU,WAAW;AAC5B,YAAM,UAAU;AAAA,IACjB;AACA,UAAM,UAAU,MAAK,YAAO,UAAP,mBAAc,SAAS;AAC5C,UAAM,WAAW,MAAK,YAAO,UAAP,mBAAc,UAAU;AAC9C,UAAM,UAAU,MAAK,YAAO,UAAP,mBAAc,SAAS;AAC5C,UAAM,QAAQ,MAAK,YAAO,UAAP,mBAAc,OAAO;AACxC,UAAM,QAAQ,MAAK,YAAO,UAAP,mBAAc,OAAO;AAAA,EACzC;AAEA,SAAO;AAAA,IACN;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACD;AACD;;;ACnJA,IAAM,sBAAN,MAAmD;AAAA,EAClD,YAAoB,SAAsB;AAAtB;AAAA,EAAuB;AAAA,EAE3C,mBACC,SACA,UACmB;AACnB,QAAI,KAAK,QAAQ,aAAa;AAC7B,aAAO,QAAQ;AAAA,QACd,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,YAAY,QAAQ;AAAA,MACrE;AAAA,IACD;AACA,WAAO,QAAQ,QAAQ,UAAU,KAAK,QAAQ,QAAQ;AAAA,EACvD;AAAA,EAEA,WAAmB;AAClB,WAAO,KAAK,QAAQ;AAAA,EACrB;AACD;AAEA,IAAM,2BAAN,MAAwD;AAAA,EACvD,YAAoB,SAA2B;AAA3B;AAAA,EAA4B;AAAA,EAEhD,mBACC,SACA,UACmB;AACnB,QAAI,KAAK,QAAQ,aAAa;AAC7B,aAAO,QAAQ;AAAA,QACd,UAAU,KAAK,QAAQ,YAAY,KAAK,QAAQ,YAAY,QAAQ;AAAA,MACrE;AAAA,IACD;AACA,WAAO,QAAQ,QAAQ,UAAU,KAAK,QAAQ,QAAQ;AAAA,EACvD;AAAA,EAEA,SAAS,SAAyB;AACjC,UAAM,QAAQ,KAAK;AAAA,MAClB,KAAK,QAAQ;AAAA,MACb,KAAK,QAAQ,YAAY,KAAK;AAAA,IAC/B;AACA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,oBAAoB,SAAsC;AACzE,MAAI,OAAO,YAAY,UAAU;AAChC,WAAO,IAAI,oBAAoB;AAAA,MAC9B,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,UAAQ,QAAQ,MAAM;AAAA,IACrB,KAAK;AACJ,aAAO,IAAI,oBAAoB,OAAO;AAAA,IACvC,KAAK;AACJ,aAAO,IAAI,yBAAyB,OAAO;AAAA,IAC5C;AACC,YAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AACD;;;AC5CO,IAAM,gBAAgB,OAAO,YAAgC;AACnE,QAAM,UAAkC,CAAC;AACzC,QAAM,WAAW,OAChB,UAGK,OAAO,UAAU,aAAa,MAAM,MAAM,IAAI;AACpD,MAAI,mCAAS,MAAM;AAClB,QAAI,QAAQ,KAAK,SAAS,UAAU;AACnC,YAAM,QAAQ,MAAM,SAAS,QAAQ,KAAK,KAAK;AAC/C,UAAI,CAAC,OAAO;AACX,eAAO;AAAA,MACR;AACA,cAAQ,eAAe,IAAI,UAAU,KAAK;AAAA,IAC3C,WAAW,QAAQ,KAAK,SAAS,SAAS;AACzC,YAAM,WAAW,SAAS,QAAQ,KAAK,QAAQ;AAC/C,YAAM,WAAW,SAAS,QAAQ,KAAK,QAAQ;AAC/C,UAAI,CAAC,YAAY,CAAC,UAAU;AAC3B,eAAO;AAAA,MACR;AACA,cAAQ,eAAe,IAAI,SAAS,KAAK,GAAG,QAAQ,IAAI,QAAQ,EAAE,CAAC;AAAA,IACpE,WAAW,QAAQ,KAAK,SAAS,UAAU;AAC1C,YAAM,QAAQ,SAAS,QAAQ,KAAK,KAAK;AACzC,UAAI,CAAC,OAAO;AACX,eAAO;AAAA,MACR;AACA,cAAQ,eAAe,IAAI,GAAG,SAAS,QAAQ,KAAK,MAAM,CAAC,IAAI,KAAK;AAAA,IACrE;AAAA,EACD;AACA,SAAO;AACR;;;ACvEA,IAAM,UAAU;AAGT,SAAS,mBAAmB,SAAiC;AACnE,QAAM,eAAe,QAAQ,QAAQ,IAAI,cAAc;AACvD,QAAM,YAAY,oBAAI,IAAI;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AACD,MAAI,CAAC,cAAc;AAClB,WAAO;AAAA,EACR;AACA,QAAM,cAAc,aAAa,MAAM,GAAG,EAAE,MAAM,KAAK;AACvD,MAAI,QAAQ,KAAK,WAAW,GAAG;AAC9B,WAAO;AAAA,EACR;AACA,MAAI,UAAU,IAAI,WAAW,KAAK,YAAY,WAAW,OAAO,GAAG;AAClE,WAAO;AAAA,EACR;AACA,SAAO;AACR;AAEO,SAAS,eAAe,OAAY;AAC1C,MAAI;AACH,SAAK,MAAM,KAAK;AAChB,WAAO;AAAA,EACR,SAAS,OAAO;AACf,WAAO;AAAA,EACR;AACD;AAGO,SAAS,mBAAmB,OAAY;AAC9C,MAAI,UAAU,QAAW;AACxB,WAAO;AAAA,EACR;AACA,QAAM,IAAI,OAAO;AACjB,MAAI,MAAM,YAAY,MAAM,YAAY,MAAM,aAAa,MAAM,MAAM;AACtE,WAAO;AAAA,EACR;AACA,MAAI,MAAM,UAAU;AACnB,WAAO;AAAA,EACR;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO;AAAA,EACR;AACA,MAAI,MAAM,QAAQ;AACjB,WAAO;AAAA,EACR;AACA,SACE,MAAM,eAAe,MAAM,YAAY,SAAS,YACjD,OAAO,MAAM,WAAW;AAE1B;AAEO,SAAS,UAAU,MAAc;AACvC,MAAI;AACH,WAAO,KAAK,MAAM,IAAI;AAAA,EACvB,SAAS,OAAO;AACf,WAAO;AAAA,EACR;AACD;AAEO,SAAS,WAAW,OAAgC;AAC1D,SAAO,OAAO,UAAU;AACzB;AAEO,SAAS,SAAS,SAAyC;AACjE,MAAI,mCAAS,iBAAiB;AAC7B,WAAO,QAAQ;AAAA,EAChB;AACA,MAAI,OAAO,eAAe,eAAe,WAAW,WAAW,KAAK,GAAG;AACtE,WAAO,WAAW;AAAA,EACnB;AACA,MAAI,OAAO,WAAW,eAAe,WAAW,OAAO,KAAK,GAAG;AAC9D,WAAO,OAAO;AAAA,EACf;AACA,QAAM,IAAI,MAAM,+BAA+B;AAChD;AAEO,SAAS,gBAAgB,QAAiB;AAChD,MAAI,CAAC,QAAQ;AACZ,WAAO;AAAA,EACR;AACA,QAAM,gBAAgB,CAAC,QAAQ,OAAO,SAAS,QAAQ;AACvD,SAAO,cAAc,SAAS,OAAO,YAAY,CAAC;AACnD;AAEO,SAAS,cAAc,QAAiB;AAC9C,QAAM,cAAc,CAAC,OAAO,QAAQ,OAAO,SAAS,QAAQ;AAC5D,MAAI,CAAC,QAAQ;AACZ,WAAO;AAAA,EACR;AACA,SAAO,YAAY,SAAS,OAAO,YAAY,CAAC;AACjD;AAEA,eAAsB,WAAW,MAA0B;AAC1D,QAAM,UAAU,IAAI,QAAQ,6BAAM,OAAO;AACzC,QAAM,aAAa,MAAM,cAAc,IAAI;AAC3C,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,cAAc,CAAC,CAAC,GAAG;AAC5D,YAAQ,IAAI,KAAK,KAAK;AAAA,EACvB;AACA,MAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AACjC,UAAM,IAAI,kBAAkB,6BAAM,IAAI;AACtC,QAAI,GAAG;AACN,cAAQ,IAAI,gBAAgB,CAAC;AAAA,IAC9B;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,OAAO,KAAa,SAA6B;AAChE,MAAI,IAAI,WAAW,GAAG,GAAG;AACxB,UAAM,IAAI,IAAI,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AACnD,QAAI,QAAQ,SAAS,CAAC,GAAG;AACxB,YAAM,IAAI,QAAQ,IAAI,CAAC,KAAK,GAAG;AAAA,IAChC;AAAA,EACD;AACA,MAAI;AACJ,MAAI;AACH,QAAI,IAAI,WAAW,MAAM,GAAG;AAC3B,aAAO;AAAA,IACR,OAAO;AACN,UAAI,UAAU,mCAAS;AACvB,UAAI,WAAW,EAAC,mCAAS,SAAS,OAAM;AACvC,kBAAU,UAAU;AAAA,MACrB;AACA,UAAI,IAAI,WAAW,GAAG,GAAG;AACxB,eAAO,IAAI,IAAI,IAAI,UAAU,CAAC,GAAG,OAAO;AAAA,MACzC,OAAO;AACN,eAAO,IAAI,IAAI,KAAK,mCAAS,OAAO;AAAA,MACrC;AAAA,IACD;AAAA,EACD,SAAS,GAAG;AACX,QAAI,aAAa,WAAW;AAC3B,UAAI,EAAC,mCAAS,UAAS;AACtB,cAAM;AAAA,UACL,eAAe,GAAG;AAAA,QACnB;AAAA,MACD;AACA,YAAM;AAAA,QACL,eAAe,GAAG;AAAA,MACnB;AAAA,IACD;AACA,UAAM;AAAA,EACP;AAKA,MAAI,mCAAS,QAAQ;AACpB,QAAI,MAAM,QAAQ,mCAAS,MAAM,GAAG;AACnC,YAAM,UAAS,mCAAS,UACrB,MAAM,QAAQ,QAAQ,MAAM,IAC3B,IAAI,QAAQ,OAAO,KAAK,GAAG,CAAC,KAC5B,IAAI,OAAO,OAAO,QAAQ,MAAM,EAAE,KAAK,GAAG,CAAC,KAC5C;AACH,aAAO,KAAK,SAAS,EAAE,MAAM,IAAI,EAAE,CAAC;AACpC,aAAO,GAAG,KAAK,SAAS,CAAC,GAAG,MAAM;AAAA,IACnC,OAAO;AACN,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,mCAAS,MAAM,GAAG;AAC3D,eAAO,KAAK,SAAS,EAAE,QAAQ,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC;AAAA,MACxD;AAAA,IACD;AAAA,EACD;AACA,QAAM,QAAQ,IAAI,IAAI,IAAI;AAI1B,QAAM,cAAc,mCAAS;AAC7B,MAAI,aAAa;AAChB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,WAAW,GAAG;AACvD,YAAM,aAAa,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,IAC7C;AAAA,EACD;AACA,SAAO;AACR;AAEO,SAAS,kBAAkB,MAAW;AAC5C,MAAI,mBAAmB,IAAI,GAAG;AAC7B,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AAEO,SAAS,QAAQ,SAA6B;AACpD,MAAI,EAAC,mCAAS,OAAM;AACnB,WAAO;AAAA,EACR;AACA,QAAM,UAAU,IAAI,QAAQ,mCAAS,OAAO;AAC5C,MAAI,mBAAmB,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,cAAc,GAAG;AACrE,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,mCAAS,IAAI,GAAG;AACzD,UAAI,iBAAiB,MAAM;AAC1B,gBAAQ,KAAK,GAAG,IAAI,MAAM,YAAY;AAAA,MACvC;AAAA,IACD;AACA,WAAO,KAAK,UAAU,QAAQ,IAAI;AAAA,EACnC;AAEA,SAAO,QAAQ;AAChB;AAEO,SAAS,UAAU,KAAa,SAA6B;AAnNpE;AAoNC,MAAI,mCAAS,QAAQ;AACpB,WAAO,QAAQ,OAAO,YAAY;AAAA,EACnC;AACA,MAAI,IAAI,WAAW,GAAG,GAAG;AACxB,UAAM,WAAU,SAAI,MAAM,GAAG,EAAE,CAAC,MAAhB,mBAAmB,MAAM,KAAK;AAC9C,QAAI,CAAC,QAAQ,SAAS,OAAO,GAAG;AAC/B,cAAO,mCAAS,QAAO,SAAS;AAAA,IACjC;AACA,WAAO,QAAQ,YAAY;AAAA,EAC5B;AACA,UAAO,mCAAS,QAAO,SAAS;AACjC;AAEO,SAAS,WACf,SACA,YACC;AACD,MAAI;AACJ,MAAI,EAAC,mCAAS,YAAU,mCAAS,UAAS;AACzC,mBAAe,WAAW,MAAM,yCAAY,SAAS,mCAAS,OAAO;AAAA,EACtE;AACA,SAAO;AAAA,IACN;AAAA,IACA,cAAc,MAAM;AACnB,UAAI,cAAc;AACjB,qBAAa,YAAY;AAAA,MAC1B;AAAA,IACD;AAAA,EACD;AACD;AAEO,SAAS,WAAW,MAAW,cAA4B;AACjE,MAAI,iBAAiB,QAAQ;AAC5B,WAAO,KAAK,MAAM,IAAI;AAAA,EACvB;AACA,SAAO;AACR;AAEO,IAAM,kBAAN,MAAM,yBAAwB,MAAM;AAAA,EAG1C,YAAY,QAA+C,SAAkB;AAE5E,UAAM,WAAW,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AAChD,SAAK,SAAS;AAGd,WAAO,eAAe,MAAM,iBAAgB,SAAS;AAAA,EACtD;AACD;AAEA,eAAsB,oBACrB,QACA,OACiD;AACjD,MAAI,SAAS,MAAM,OAAO,WAAW,EAAE,SAAS,KAAK;AAErD,MAAI,OAAO,QAAQ;AAClB,UAAM,IAAI,gBAAgB,OAAO,MAAM;AAAA,EACxC;AACA,SAAO,OAAO;AACf;;;ACpQO,IAAM,UAAU,CAAC,OAAO,QAAQ,OAAO,SAAS,QAAQ;AAQxD,IAAM,eAAe,CAI3B,QACA,WACI;AACJ,SAAO;AAAA,IACN;AAAA,IACA;AAAA,EACD;AACD;;;AC1BO,IAAM,oBAAoB,CAAC,YAChC;AAAA,EACA,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,SAAS;AAAA,EACT,MAAM,KAAK,KAAK,SAAS;AAX3B;AAYG,UAAM,WACL,kBAAO,YAAP,mBAAgB;AAAA,MAAK,CAAC,WAAQ;AAblC,YAAAA;AAcK,iBAAAA,MAAA,OAAO,WAAP,gBAAAA,IAAe,UACZ,IAAI,WAAW,OAAO,OAAO,OAAO,WAAW,EAAE,KAClD,IAAI,WAAW,OAAO,OAAO,OAAO,UAAU,EAAE,IAC/C;AAAA;AAAA,UAJJ,mBAKG,WAAU,OAAO;AACrB,QAAI,QAAQ;AACX,UAAI,SAAS;AACb,WAAI,YAAO,WAAP,mBAAe,QAAQ;AAC1B,YAAI,OAAO,WAAW,OAAO,OAAO,MAAM,GAAG;AAC5C,mBAAS,OAAO,QAAQ,OAAO,OAAO,QAAQ,EAAE;AAChD,cAAI,OAAO,OAAO,SAAS;AAC1B,kBAAM,IAAI,QAAQ,OAAO,OAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,UAC9D;AAAA,QACD;AAAA,MACD;AACA,WAAI,YAAO,WAAP,mBAAe,SAAS;AAC3B,YAAI,OAAO,WAAW,OAAO,OAAO,OAAO,GAAG;AAC7C,mBAAS,OAAO,QAAQ,OAAO,OAAO,SAAS,EAAE;AAAA,QAClD;AAAA,MACD;AACA,YAAM,YAAY,OAAO,OAAO,MAAM;AACtC,UAAI,WAAW;AACd,YAAI,OAAO,iCACP,UADO;AAAA,UAEV,QAAQ,UAAU;AAAA,UAClB,QAAQ,UAAU;AAAA,QACnB;AACA,YAAI,EAAC,mCAAS,oBAAmB;AAChC,iBAAO,iCACH,OADG;AAAA,YAEN,MAAM,UAAU,QACb,MAAM,oBAAoB,UAAU,OAAO,mCAAS,IAAI,IACxD,mCAAS;AAAA,YACZ,QAAQ,UAAU,SACf,MAAM,oBAAoB,UAAU,QAAQ,mCAAS,MAAM,IAC3D,mCAAS;AAAA,YACZ,OAAO,UAAU,QACd,MAAM,oBAAoB,UAAU,OAAO,mCAAS,KAAK,IACzD,mCAAS;AAAA,UACb;AAAA,QACD;AACA,eAAO;AAAA,UACN;AAAA,UACA,SAAS;AAAA,QACV;AAAA,MACD;AAAA,IACD;AACA,WAAO;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,EACD;AACD;AAEM,IAAM,cAAc,CAC1B,WACI;AACJ,iBAAe,OAAO,KAAa,SAA6B;AAC/D,UAAM,OAAO,gDACT,SACA,UAFS;AAAA,MAGZ,SAAS,CAAC,IAAI,iCAAQ,YAAW,CAAC,GAAI,kBAAkB,UAAU,CAAC,CAAC,CAAC;AAAA,IACtE;AAEA,QAAI,iCAAQ,eAAe;AAC1B,UAAI;AACH,eAAO,MAAM,YAAY,KAAK,IAAI;AAAA,MACnC,SAAS,OAAO;AACf,eAAO;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,YACN,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,SACC;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AACA,WAAO,MAAM,YAAY,KAAK,IAAI;AAAA,EACnC;AACA,SAAO;AACR;;;AC3FO,SAASC,QAAO,KAAa,QAA4B;AAC/D,MAAI,EAAE,SAAS,QAAQ,MAAM,IAAI,UAAU;AAAA,IAC1C,OAAO,CAAC;AAAA,IACR,QAAQ,CAAC;AAAA,IACT,SAAS;AAAA,EACV;AACA,MAAI,WAAW,IAAI,WAAW,MAAM,IACjC,IAAI,MAAM,GAAG,EAAE,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,IACnC,WAAW;AAKd,MAAI,IAAI,WAAW,GAAG,GAAG;AACxB,UAAM,IAAI,IAAI,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AACnD,QAAI,QAAQ,SAAS,CAAC,GAAG;AACxB,YAAM,IAAI,QAAQ,IAAI,CAAC,KAAK,GAAG;AAAA,IAChC;AAAA,EACD;AAEA,MAAI,CAAC,SAAS,SAAS,GAAG,EAAG,aAAY;AACzC,MAAI,CAAC,MAAM,QAAQ,IAAI,IAAI,QAAQ,UAAU,EAAE,EAAE,MAAM,GAAG;AAC1D,QAAM,cAAc,IAAI,gBAAgB,QAAQ;AAChD,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,SAAS,CAAC,CAAC,GAAG;AACvD,QAAI,SAAS,KAAM;AACnB,gBAAY,IAAI,KAAK,OAAO,KAAK,CAAC;AAAA,EACnC;AACA,MAAI,QAAQ;AACX,QAAI,MAAM,QAAQ,MAAM,GAAG;AAC1B,YAAM,aAAa,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC,MAAM,EAAE,WAAW,GAAG,CAAC;AAClE,iBAAW,CAAC,OAAO,GAAG,KAAK,WAAW,QAAQ,GAAG;AAChD,cAAM,QAAQ,OAAO,KAAK;AAC1B,eAAO,KAAK,QAAQ,KAAK,KAAK;AAAA,MAC/B;AAAA,IACD,OAAO;AACN,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAClD,eAAO,KAAK,QAAQ,IAAI,GAAG,IAAI,OAAO,KAAK,CAAC;AAAA,MAC7C;AAAA,IACD;AAAA,EACD;AAEA,SAAO,KAAK,MAAM,GAAG,EAAE,IAAI,kBAAkB,EAAE,KAAK,GAAG;AACvD,MAAI,KAAK,WAAW,GAAG,EAAG,QAAO,KAAK,MAAM,CAAC;AAC7C,MAAI,mBAAmB,YAAY,SAAS;AAC5C,qBACC,iBAAiB,SAAS,IAAI,IAAI,gBAAgB,GAAG,QAAQ,OAAO,KAAK,IAAI;AAC9E,MAAI,CAAC,SAAS,WAAW,MAAM,GAAG;AACjC,WAAO,GAAG,QAAQ,GAAG,IAAI,GAAG,gBAAgB;AAAA,EAC7C;AACA,QAAM,OAAO,IAAI,IAAI,GAAG,IAAI,GAAG,gBAAgB,IAAI,QAAQ;AAC3D,SAAO;AACR;;;ACvCO,IAAM,cAAc,OAO1B,KACA,YAOI;AAjCL;AAkCC,QAAM;AAAA,IACL;AAAA,IACA,KAAK;AAAA,IACL,SAAS;AAAA,EACV,IAAI,MAAM,kBAAkB,KAAK,OAAO;AACxC,QAAM,QAAQ,SAAS,IAAI;AAC3B,QAAM,aAAa,IAAI,gBAAgB;AACvC,QAAM,UAAS,UAAK,WAAL,YAAe,WAAW;AACzC,QAAM,OAAOC,QAAO,OAAO,IAAI;AAC/B,QAAM,OAAO,QAAQ,IAAI;AACzB,QAAM,UAAU,MAAM,WAAW,IAAI;AACrC,QAAM,SAAS,UAAU,OAAO,IAAI;AACpC,MAAI,UAAU,iCACV,OADU;AAAA,IAEb,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAIA,aAAW,aAAa,MAAM,WAAW;AACxC,QAAI,WAAW;AACd,YAAM,MAAM,MAAM,UAAU,OAAO;AACnC,UAAI,eAAe,QAAQ;AAC1B,kBAAU;AAAA,MACX;AAAA,IACD;AAAA,EACD;AACA,MACE,YAAa,WACb,OAAQ,QAAgB,WAAW,cACpC,SAAO,wCAAS,SAAT,mBAAe,UAAS,YAC9B;AACD,QAAI,EAAE,YAAY,UAAU;AAC3B,cAAQ,SAAS;AAAA,IAClB;AAAA,EACD;AAEA,QAAM,EAAE,cAAAC,cAAa,IAAI,WAAW,MAAM,UAAU;AACpD,MAAI,WAAW,MAAM,MAAM,QAAQ,KAAK,OAAO;AAC/C,EAAAA,cAAa;AAEb,QAAM,kBAAkB;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,EACV;AAEA,aAAW,cAAc,MAAM,YAAY;AAC1C,QAAI,YAAY;AACf,YAAM,IAAI,MAAM,WAAW,iCACvB,kBADuB;AAAA,QAE1B,YAAU,wCAAS,gBAAT,mBAAsB,iBAC7B,SAAS,MAAM,IACf;AAAA,MACJ,EAAC;AACD,UAAI,aAAa,UAAU;AAC1B,mBAAW;AAAA,MACZ,WAAW,aAAa,QAAQ;AAC/B,mBAAW,EAAE;AAAA,MACd;AAAA,IACD;AAAA,EACD;AAKA,MAAI,SAAS,IAAI;AAChB,UAAM,UAAU,QAAQ,WAAW;AACnC,QAAI,CAAC,SAAS;AACb,aAAO;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACR;AAAA,IACD;AACA,UAAM,eAAe,mBAAmB,QAAQ;AAChD,UAAM,iBAAiB;AAAA,MACtB,MAAM;AAAA,MACN;AAAA,MACA,SAAS;AAAA,IACV;AACA,QAAI,iBAAiB,UAAU,iBAAiB,QAAQ;AACvD,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,YAAMC,WAAS,aAAQ,eAAR,YAAsB;AACrC,YAAM,OAAO,MAAMA,QAAO,IAAI;AAC9B,qBAAe,OAAO;AAAA,IACvB,OAAO;AACN,qBAAe,OAAO,MAAM,SAAS,YAAY,EAAE;AAAA,IACpD;AAKA,QAAI,mCAAS,QAAQ;AACpB,UAAI,QAAQ,UAAU,CAAC,QAAQ,mBAAmB;AACjD,uBAAe,OAAO,MAAM;AAAA,UAC3B,QAAQ;AAAA,UACR,eAAe;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAEA,eAAW,aAAa,MAAM,WAAW;AACxC,UAAI,WAAW;AACd,cAAM,UAAU,iCACZ,iBADY;AAAA,UAEf,YAAU,wCAAS,gBAAT,mBAAsB,iBAC7B,SAAS,MAAM,IACf;AAAA,QACJ,EAAC;AAAA,MACF;AAAA,IACD;AAEA,QAAI,mCAAS,OAAO;AACnB,aAAO,eAAe;AAAA,IACvB;AAEA,WAAO;AAAA,MACN,MAAM,eAAe;AAAA,MACrB,OAAO;AAAA,IACR;AAAA,EACD;AACA,QAAM,UAAS,wCAAS,eAAT,YAAuB;AACtC,QAAM,eAAe,MAAM,SAAS,KAAK;AACzC,QAAM,iBAAiB,eAAe,YAAY;AAClD,QAAM,cAAc,iBAAiB,MAAM,OAAO,YAAY,IAAI;AAIlE,QAAM,eAAe;AAAA,IACpB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,OAAO,iCACH,cADG;AAAA,MAEN,QAAQ,SAAS;AAAA,MACjB,YAAY,SAAS;AAAA,IACtB;AAAA,EACD;AACA,aAAW,WAAW,MAAM,SAAS;AACpC,QAAI,SAAS;AACZ,YAAM,QAAQ,iCACV,eADU;AAAA,QAEb,YAAU,wCAAS,gBAAT,mBAAsB,iBAC7B,SAAS,MAAM,IACf;AAAA,MACJ,EAAC;AAAA,IACF;AAAA,EACD;AAEA,MAAI,mCAAS,OAAO;AACnB,UAAM,gBAAgB,oBAAoB,QAAQ,KAAK;AACvD,UAAM,iBAAgB,aAAQ,iBAAR,YAAwB;AAC9C,QAAI,MAAM,cAAc,mBAAmB,eAAe,QAAQ,GAAG;AACpE,iBAAW,WAAW,MAAM,SAAS;AACpC,YAAI,SAAS;AACZ,gBAAM,QAAQ,eAAe;AAAA,QAC9B;AAAA,MACD;AACA,YAAM,QAAQ,cAAc,SAAS,aAAa;AAClD,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,KAAK,CAAC;AACzD,aAAO,MAAM,YAAY,KAAK,iCAC1B,UAD0B;AAAA,QAE7B,cAAc,gBAAgB;AAAA,MAC/B,EAAC;AAAA,IACF;AAAA,EACD;AAEA,MAAI,mCAAS,OAAO;AACnB,UAAM,IAAI;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,iBAAiB,cAAc;AAAA,IAChC;AAAA,EACD;AACA,SAAO;AAAA,IACN,MAAM;AAAA,IACN,OAAO,iCACH,cADG;AAAA,MAEN,QAAQ,SAAS;AAAA,MACjB,YAAY,SAAS;AAAA,IACtB;AAAA,EACD;AACD;", "names": ["_a", "getURL", "getURL", "clearTimeout", "parser"]}