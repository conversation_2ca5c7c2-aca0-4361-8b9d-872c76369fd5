{"name": "@better-fetch/fetch", "version": "1.1.18", "main": "./dist/index.cjs", "type": "module", "module": "./dist/index.js", "react-native": "./dist/index.js", "types": "./dist/index.d.ts", "devDependencies": {"@happy-dom/global-registrator": "^14.7.1", "@testing-library/dom": "^10.0.0", "@testing-library/react": "^15.0.3", "@types/bun": "^1.1.0", "@types/node": "^20.11.30", "bumpp": "^9.4.1", "global-jsdom": "^24.0.0", "h3": "^1.11.1", "jsdom": "^24.0.0", "listhen": "^1.7.2", "mocha": "^10.4.0", "tsup": "^8.0.2", "typescript": "^5.4.5", "vitest": "^1.5.0", "zod": "^3.24.1"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "bump": "bumpp", "test:watch": "vitest watch", "lint": "biome check .", "release": "bun run build && npm publish --access public", "lint:fix": "biome check . --apply", "typecheck": "tsc --noEmit"}}