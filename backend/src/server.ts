import "dotenv/config";
import Fastify from "fastify";
import cors from "@fastify/cors";
import helmet from "@fastify/helmet";
import swagger from "@fastify/swagger";
import swaggerUi from "@fastify/swagger-ui";
import prismaPlugin from "./plugins/prisma";
import authPlugin from "./plugins/auth.plugin";
import { authRoutes } from "./routes/auth.routes";
import { addressRoutes } from "./routes/address.routes";
import { locationRoutes } from "./routes/location.routes";
import { appsRoutes } from "./routes/apps.routes";
import { smsAlertsRoutes } from "./routes/sms-alerts.routes";

const server = Fastify({
  logger: {
    level: process.env.NODE_ENV === "production" ? "info" : "debug",
    transport: process.env.NODE_ENV === "development" ? {
      target: "pino-pretty",
      options: {
        translateTime: "HH:MM:ss Z",
        ignore: "pid,hostname",
      },
    } : undefined,
  },
});

// Register plugins
server.register(cors, {
  origin: process.env.CORS_ORIGIN || process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true,
});

server.register(helmet, {
  contentSecurityPolicy: false,
});

server.register(swagger, {
  openapi: {
    info: {
      title: "ShipTag API",
      description: "ShipTag backend API documentation",
      version: "1.0.0",
    },
    servers: [
      {
        url: process.env.API_URL || "http://localhost:3001",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
    },
  },
});

server.register(swaggerUi, {
  routePrefix: "/documentation",
  uiConfig: {
    docExpansion: "list",
    deepLinking: false,
  },
  staticCSP: true,
});

// Register custom plugins
server.register(prismaPlugin);
server.register(authPlugin);

// Register routes
server.register(authRoutes, { prefix: "/api" });
server.register(addressRoutes, { prefix: "/api" });
server.register(locationRoutes, { prefix: "/api" });
server.register(smsAlertsRoutes, { prefix: "/api" });
server.register(appsRoutes);

// Health check
server.get("/health", async () => {
  return { status: "ok", timestamp: new Date().toISOString() };
});

// Start server
const start = async () => {
  try {
    const port = parseInt(process.env.PORT || "3001");
    const host = process.env.HOST || "0.0.0.0";
    
    await server.listen({ port, host });
    
    console.log(`Server listening on http://${host}:${port}`);
    console.log(`Swagger documentation available at http://${host}:${port}/documentation`);
  } catch (err) {
    server.log.error(err);
    process.exit(1);
  }
};

start();