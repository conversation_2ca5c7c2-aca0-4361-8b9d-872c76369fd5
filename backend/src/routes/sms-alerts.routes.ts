import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";

interface SmsAlert {
  id: string;
  message: string;
  triggerType: string;
  language: string;
  status: "Active" | "Inactive";
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface QueryParams {
  search?: string;
  triggerType?: string;
  language?: string;
  status?: string;
  page?: string;
  limit?: string;
}

// Mock data for SMS alerts - in a real implementation, this would come from a database
const mockSmsAlerts: SmsAlert[] = [
  {
    id: "default-sms-alert",
    message: "Your shipment is being delivered by {{deliveryCompany}}, tracking number is {{trackingNumber}} {{link}}",
    triggerType: "Tracking",
    language: "English",
    status: "Active",
    isDefault: true,
    createdAt: new Date("2024-01-01T00:00:00Z"),
    updatedAt: new Date("2024-01-01T00:00:00Z"),
  },
  {
    id: "delivered-sms-alert",
    message: "Your package has been delivered successfully. Thank you for choosing our service!",
    triggerType: "Delivered",
    language: "English",
    status: "Active",
    isDefault: false,
    createdAt: new Date("2024-01-02T00:00:00Z"),
    updatedAt: new Date("2024-01-02T00:00:00Z"),
  },
  {
    id: "failed-delivery-sms-alert",
    message: "Delivery attempt failed. Please contact customer service for rescheduling.",
    triggerType: "Failed",
    language: "English",
    status: "Active",
    isDefault: false,
    createdAt: new Date("2024-01-03T00:00:00Z"),
    updatedAt: new Date("2024-01-03T00:00:00Z"),
  },
  {
    id: "out-for-delivery-sms-alert",
    message: "Your package is out for delivery and will arrive today.",
    triggerType: "Out for Delivery",
    language: "English",
    status: "Active",
    isDefault: false,
    createdAt: new Date("2024-01-04T00:00:00Z"),
    updatedAt: new Date("2024-01-04T00:00:00Z"),
  },
  {
    id: "tracking-arabic-sms-alert",
    message: "يتم توصيل شحنتك بواسطة {{deliveryCompany}}، رقم التتبع {{trackingNumber}} {{link}}",
    triggerType: "Tracking",
    language: "Arabic",
    status: "Active",
    isDefault: false,
    createdAt: new Date("2024-01-05T00:00:00Z"),
    updatedAt: new Date("2024-01-05T00:00:00Z"),
  },
  {
    id: "delivered-arabic-sms-alert",
    message: "تم توصيل الطرد بنجاح. شكراً لاختيارك خدماتنا!",
    triggerType: "Delivered",
    language: "Arabic",
    status: "Active",
    isDefault: false,
    createdAt: new Date("2024-01-06T00:00:00Z"),
    updatedAt: new Date("2024-01-06T00:00:00Z"),
  },
  {
    id: "inactive-sms-alert",
    message: "This is an inactive SMS alert for testing purposes.",
    triggerType: "Testing",
    language: "English",
    status: "Inactive",
    isDefault: false,
    createdAt: new Date("2024-01-07T00:00:00Z"),
    updatedAt: new Date("2024-01-07T00:00:00Z"),
  },
];

function filterSmsAlerts(alerts: SmsAlert[], filters: Omit<QueryParams, 'page' | 'limit'>): SmsAlert[] {
  let filtered = [...alerts];

  // Global search in message content
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filtered = filtered.filter(alert => 
      alert.message.toLowerCase().includes(searchTerm)
    );
  }

  // Filter by trigger type
  if (filters.triggerType) {
    filtered = filtered.filter(alert => 
      alert.triggerType.toLowerCase() === filters.triggerType!.toLowerCase()
    );
  }

  // Filter by language
  if (filters.language) {
    filtered = filtered.filter(alert => 
      alert.language.toLowerCase() === filters.language!.toLowerCase()
    );
  }

  // Filter by status
  if (filters.status) {
    filtered = filtered.filter(alert => 
      alert.status.toLowerCase() === filters.status!.toLowerCase()
    );
  }

  return filtered;
}

export async function smsAlertsRoutes(fastify: FastifyInstance) {
  // Get all SMS alerts with filtering and pagination
  fastify.get<{ Querystring: QueryParams }>("/sms-alerts", {
    preHandler: fastify.authenticate,
    schema: {
      querystring: {
        type: "object",
        properties: {
          search: { type: "string" },
          triggerType: { type: "string" },
          language: { type: "string" },
          status: { type: "string", enum: ["Active", "Inactive"] },
          page: { type: "string", pattern: "^[0-9]+$" },
          limit: { type: "string", pattern: "^[0-9]+$" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  message: { type: "string" },
                  triggerType: { type: "string" },
                  language: { type: "string" },
                  status: { type: "string", enum: ["Active", "Inactive"] },
                  isDefault: { type: "boolean" },
                  createdAt: { type: "string", format: "date-time" },
                  updatedAt: { type: "string", format: "date-time" },
                },
              },
            },
            total: { type: "number" },
            page: { type: "number" },
            limit: { type: "number" },
            totalPages: { type: "number" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const {
        search,
        triggerType,
        language,
        status,
        page = "1",
        limit = "20",
      } = request.query;

      const filters = { search, triggerType, language, status };
      const pagination = {
        page: parseInt(page, 10),
        limit: Math.min(parseInt(limit, 10), 100), // Max 100 items per page
      };

      // Filter the alerts
      let filteredAlerts = filterSmsAlerts(mockSmsAlerts, filters);

      // Sort to ensure default message appears first
      filteredAlerts.sort((a, b) => {
        if (a.isDefault && !b.isDefault) return -1;
        if (!a.isDefault && b.isDefault) return 1;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

      // Apply pagination
      const total = filteredAlerts.length;
      const totalPages = Math.ceil(total / pagination.limit);
      const startIndex = (pagination.page - 1) * pagination.limit;
      const endIndex = startIndex + pagination.limit;
      const paginatedAlerts = filteredAlerts.slice(startIndex, endIndex);

      return reply.send({
        success: true,
        data: paginatedAlerts,
        total,
        page: pagination.page,
        limit: pagination.limit,
        totalPages,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        message: "Failed to fetch SMS alerts",
      });
    }
  });
}
