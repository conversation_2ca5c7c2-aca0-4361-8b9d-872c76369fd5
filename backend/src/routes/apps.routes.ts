import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import { PrismaClient } from "@prisma/client";
import { validateSession } from "../auth/auth";

const prisma = new PrismaClient();

interface AppToggleParams {
  id: string;
}

interface AppToggleBody {
  enabled: boolean;
}

// Authentication middleware
async function authenticate(request: FastifyRequest, reply: FastifyReply) {
  const token = request.headers.authorization?.replace("Bearer ", "");
  
  if (!token) {
    return reply.status(401).send({
      success: false,
      error: "UNAUTHORIZED",
      message: "No authorization token provided",
      code: 401,
    });
  }

  // Handle demo token for development
  if (token === 'demo-token-for-development' && process.env.NODE_ENV !== 'production') {
    // Use the seeded user for demo purposes
    const demoUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (demoUser) {
      request.userId = demoUser.id;
      return;
    }
  }

  const session = await validateSession(token);
  if (!session) {
    return reply.status(401).send({
      success: false,
      error: "UNAUTHORIZED", 
      message: "Invalid or expired session",
      code: 401,
    });
  }

  request.userId = session.userId;
}

export async function appsRoutes(fastify: FastifyInstance) {
  // Get all apps with user permissions
  fastify.get("/api/apps", {
    preHandler: authenticate,
    schema: {
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: "string" },
                  logo: { type: "string" },
                  subtitle: { type: "string" },
                  category: { type: ["string", "null"] },
                  isEnabled: { type: "boolean" },
                  canToggle: { type: "boolean" },
                  requiresAdminApproval: { type: "boolean" },
                  status: { type: "string" },
                  permissions: {
                    type: "object",
                    properties: {
                      canView: { type: "boolean" },
                      canEnable: { type: "boolean" },
                      canDisable: { type: "boolean" },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const userId = request.userId;

      // Get all apps with user permissions
      const apps = await prisma.app.findMany({
        include: {
          userAppPermissions: {
            where: { userId },
          },
        },
      });

      const appsWithPermissions = apps.map(app => {
        const userPermission = app.userAppPermissions[0];
        
        return {
          id: app.id,
          name: app.name,
          logo: app.logo,
          subtitle: app.subtitle,
          category: app.category,
          isEnabled: userPermission?.isEnabled || false,
          canToggle: userPermission?.canEnable || userPermission?.canDisable || false,
          requiresAdminApproval: app.requiresAdminApproval,
          status: userPermission?.isEnabled ? 'active' : 
                  userPermission?.approvalStatus === 'PENDING' ? 'pending_review' : 'disabled',
          permissions: {
            canView: userPermission?.canView || false,
            canEnable: userPermission?.canEnable || false,
            canDisable: userPermission?.canDisable || false,
          },
        };
      });

      return reply.send({
        success: true,
        data: appsWithPermissions,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch apps",
        code: 500,
      });
    }
  });

  // Enable specific app
  fastify.post<{ Params: AppToggleParams; Body: AppToggleBody }>("/api/apps/:id/enable", {
    preHandler: authenticate,
    schema: {
      params: {
        type: "object",
        required: ["id"],
        properties: {
          id: { type: "string" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                success: { type: "boolean" },
                status: { type: "string" },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { id: appId } = request.params;
      const userId = request.userId;

      // Check if app exists
      const app = await prisma.app.findUnique({
        where: { id: appId },
      });

      if (!app) {
        return reply.status(404).send({
          success: false,
          error: "NOT_FOUND",
          message: "App not found",
          code: 404,
        });
      }

      // Get or create user app permission
      let userPermission = await prisma.userAppPermission.findUnique({
        where: {
          userId_appId: {
            userId,
            appId,
          },
        },
      });

      if (!userPermission) {
        return reply.status(403).send({
          success: false,
          error: "FORBIDDEN",
          message: "You don't have permission to enable this app",
          code: 403,
        });
      }

      if (!userPermission.canEnable) {
        return reply.status(403).send({
          success: false,
          error: "FORBIDDEN",
          message: "You don't have permission to enable this app",
          code: 403,
        });
      }

      // Update user permission
      const status = app.requiresAdminApproval ? 'pending_review' : 'active';
      const approvalStatus = app.requiresAdminApproval ? 'PENDING' : 'APPROVED';

      await prisma.userAppPermission.update({
        where: {
          userId_appId: {
            userId,
            appId,
          },
        },
        data: {
          isEnabled: !app.requiresAdminApproval,
          approvalStatus,
        },
      });

      return reply.send({
        success: true,
        data: {
          success: true,
          status,
        },
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: "INTERNAL_SERVER_ERROR",
        message: "Failed to enable app",
        code: 500,
      });
    }
  });

  // Disable specific app
  fastify.post<{ Params: AppToggleParams }>("/api/apps/:id/disable", {
    preHandler: authenticate,
    schema: {
      params: {
        type: "object",
        required: ["id"],
        properties: {
          id: { type: "string" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                success: { type: "boolean" },
                status: { type: "string" },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { id: appId } = request.params;
      const userId = request.userId;

      // Check if app exists
      const app = await prisma.app.findUnique({
        where: { id: appId },
      });

      if (!app) {
        return reply.status(404).send({
          success: false,
          error: "NOT_FOUND",
          message: "App not found",
          code: 404,
        });
      }

      // Get user app permission
      const userPermission = await prisma.userAppPermission.findUnique({
        where: {
          userId_appId: {
            userId,
            appId,
          },
        },
      });

      if (!userPermission || !userPermission.canDisable) {
        return reply.status(403).send({
          success: false,
          error: "FORBIDDEN",
          message: "You don't have permission to disable this app",
          code: 403,
        });
      }

      // Update user permission
      await prisma.userAppPermission.update({
        where: {
          userId_appId: {
            userId,
            appId,
          },
        },
        data: {
          isEnabled: false,
          approvalStatus: null,
        },
      });

      return reply.send({
        success: true,
        data: {
          success: true,
          status: 'disabled',
        },
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: "INTERNAL_SERVER_ERROR",
        message: "Failed to disable app",
        code: 500,
      });
    }
  });

  // Get app details
  fastify.get<{ Params: AppToggleParams }>("/api/apps/:id", {
    preHandler: authenticate,
    schema: {
      params: {
        type: "object",
        required: ["id"],
        properties: {
          id: { type: "string" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                id: { type: "string" },
                name: { type: "string" },
                description: { type: ["string", "null"] },
                logo: { type: "string" },
                subtitle: { type: "string" },
                category: { type: "string" },
                features: { type: "array", items: { type: "string" } },
                documentation: { type: ["object", "null"] },
                settings: { type: ["object", "null"] },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { id: appId } = request.params;
      const userId = request.userId;

      // Check user permission to view app
      const userPermission = await prisma.userAppPermission.findUnique({
        where: {
          userId_appId: {
            userId,
            appId,
          },
        },
      });

      if (!userPermission || !userPermission.canView) {
        return reply.status(403).send({
          success: false,
          error: "FORBIDDEN",
          message: "You don't have permission to view this app",
          code: 403,
        });
      }

      // Get app details
      const app = await prisma.app.findUnique({
        where: { id: appId },
      });

      if (!app) {
        return reply.status(404).send({
          success: false,
          error: "NOT_FOUND",
          message: "App not found",
          code: 404,
        });
      }

      return reply.send({
        success: true,
        data: {
          id: app.id,
          name: app.name,
          description: app.description,
          logo: app.logo,
          subtitle: app.subtitle,
          category: app.category || '',
          features: app.features,
          documentation: app.documentation,
          settings: app.settings,
        },
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch app details",
        code: 500,
      });
    }
  });
}