import { FastifyInstance, FastifyRequest, FastifyReply } from "fastify";
import {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  exportCustomersCSV,
  getCustomerOrderCount,
} from "../services/address.service";
import { AddressType } from "@prisma/client";

interface QueryParams {
  type?: AddressType;
  search?: string;
  name?: string;
  phone?: string;
  cityName?: string;
  postalCode?: string;
  page?: string;
  limit?: string;
}

interface AddressParams {
  id: string;
}

interface CreateAddressBody {
  name: string;
  phone: string;
  address1: string;
  address2?: string;
  cityName: string;
  cityId?: string;
  state?: string;
  postalCode?: string;
  country: string;
  lat?: number;
  long?: number;
}

interface UpdateAddressBody {
  name?: string;
  phone?: string;
  address1?: string;
  address2?: string;
  cityName?: string;
  cityId?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  lat?: number;
  long?: number;
}

export async function addressRoutes(fastify: FastifyInstance) {
  // Get all addresses (customers)
  fastify.get<{ Querystring: QueryParams }>("/addresses", {
    preHandler: fastify.authenticate,
    schema: {
      querystring: {
        type: "object",
        properties: {
          type: { type: "string", enum: Object.values(AddressType) },
          search: { type: "string" },
          name: { type: "string" },
          phone: { type: "string" },
          cityName: { type: "string" },
          postalCode: { type: "string" },
          page: { type: "string", pattern: "^[0-9]+$" },
          limit: { type: "string", pattern: "^[0-9]+$" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: ["string", "null"] },
                  phone: { type: ["string", "null"] },
                  address1: { type: ["string", "null"] },
                  address2: { type: ["string", "null"] },
                  cityName: { type: ["string", "null"] },
                  state: { type: ["string", "null"] },
                  postalCode: { type: ["string", "null"] },
                  country: { type: ["string", "null"] },
                  type: { type: "string" },
                  createdAt: { type: "string" },
                  updatedAt: { type: "string" },
                },
              },
            },
            total: { type: "number" },
            page: { type: "number" },
            limit: { type: "number" },
            totalPages: { type: "number" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const {
        type = AddressType.CUSTOMER,
        search,
        name,
        phone,
        cityName,
        postalCode,
        page = "1",
        limit = "20",
      } = request.query;

      // Only allow fetching CUSTOMER type addresses for now
      if (type !== AddressType.CUSTOMER) {
        return reply.status(400).send({
          success: false,
          message: "Only CUSTOMER type addresses are supported",
        });
      }

      const filters = { search, name, phone, cityName, postalCode };
      const pagination = {
        page: parseInt(page, 10),
        limit: Math.min(parseInt(limit, 10), 100), // Max 100 items per page
      };

      const result = await getCustomers(request.userId!, filters, pagination);

      // Add order count for each customer
      const dataWithOrderCount = await Promise.all(
        result.data.map(async (customer) => ({
          ...customer,
          orderCount: await getCustomerOrderCount(customer.id),
        }))
      );

      return reply.send({
        success: true,
        ...result,
        data: dataWithOrderCount,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        message: "Failed to fetch addresses",
      });
    }
  });

  // Get single address
  fastify.get<{ Params: AddressParams }>("/addresses/:id", {
    preHandler: fastify.authenticate,
    schema: {
      params: {
        type: "object",
        required: ["id"],
        properties: {
          id: { type: "string", format: "uuid" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                id: { type: "string" },
                name: { type: ["string", "null"] },
                phone: { type: ["string", "null"] },
                address1: { type: ["string", "null"] },
                address2: { type: ["string", "null"] },
                cityName: { type: ["string", "null"] },
                state: { type: ["string", "null"] },
                postalCode: { type: ["string", "null"] },
                country: { type: ["string", "null"] },
                type: { type: "string" },
                createdAt: { type: "string" },
                updatedAt: { type: "string" },
                orderCount: { type: "number" },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      const address = await getCustomerById(request.userId!, id);

      if (!address) {
        return reply.status(404).send({
          success: false,
          message: "Address not found",
        });
      }

      const orderCount = await getCustomerOrderCount(address.id);

      return reply.send({
        success: true,
        data: {
          ...address,
          orderCount,
        },
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        message: "Failed to fetch address",
      });
    }
  });

  // Create address
  fastify.post<{ Body: CreateAddressBody }>("/addresses", {
    preHandler: fastify.authenticate,
    schema: {
      body: {
        type: "object",
        required: ["name", "phone", "address1", "cityName", "country"],
        properties: {
          name: { type: "string", minLength: 3 },
          phone: { type: "string", pattern: "^\\+?[0-9]{10,15}$" },
          address1: { type: "string" },
          address2: { type: "string" },
          cityName: { type: "string" },
          cityId: { type: "string", format: "uuid" },
          state: { type: "string" },
          postalCode: { type: "string" },
          country: { type: "string", minLength: 2, maxLength: 2 },
          lat: { type: "number", minimum: -90, maximum: 90 },
          long: { type: "number", minimum: -180, maximum: 180 },
        },
      },
      response: {
        201: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                id: { type: "string" },
                name: { type: "string" },
                phone: { type: "string" },
                type: { type: "string" },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const address = await createCustomer(request.userId!, request.body);

      return reply.status(201).send({
        success: true,
        data: address,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        message: "Failed to create address",
      });
    }
  });

  // Update address
  fastify.put<{ Params: AddressParams; Body: UpdateAddressBody }>("/addresses/:id", {
    preHandler: fastify.authenticate,
    schema: {
      params: {
        type: "object",
        required: ["id"],
        properties: {
          id: { type: "string", format: "uuid" },
        },
      },
      body: {
        type: "object",
        properties: {
          name: { type: "string", minLength: 3 },
          phone: { type: "string", pattern: "^\\+?[0-9]{10,15}$" },
          address1: { type: "string" },
          address2: { type: "string" },
          cityName: { type: "string" },
          cityId: { type: "string", format: "uuid" },
          state: { type: "string" },
          postalCode: { type: "string" },
          country: { type: "string", minLength: 2, maxLength: 2 },
          lat: { type: "number", minimum: -90, maximum: 90 },
          long: { type: "number", minimum: -180, maximum: 180 },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            data: {
              type: "object",
              properties: {
                id: { type: "string" },
                name: { type: ["string", "null"] },
                phone: { type: ["string", "null"] },
                type: { type: "string" },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      const address = await updateCustomer(request.userId!, id, request.body);

      if (!address) {
        return reply.status(404).send({
          success: false,
          message: "Address not found",
        });
      }

      return reply.send({
        success: true,
        data: address,
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        message: "Failed to update address",
      });
    }
  });

  // Check if address can be deleted
  fastify.get<{ Params: AddressParams }>("/addresses/:id/can-delete", {
    preHandler: fastify.authenticate,
    schema: {
      params: {
        type: "object",
        required: ["id"],
        properties: {
          id: { type: "string", format: "uuid" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            canDelete: { type: "boolean" },
            reason: { type: "string" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      
      // Check if address exists and belongs to user
      const address = await getCustomerById(request.userId!, id);
      if (!address) {
        return reply.status(404).send({
          success: false,
          canDelete: false,
          reason: "Address not found",
        });
      }

      // Check if customer has linked orders
      const orderCount = await getCustomerOrderCount(id);
      const canDelete = orderCount === 0;

      return reply.send({
        success: true,
        canDelete,
        reason: canDelete ? "Customer can be deleted" : "Cannot delete customer with existing orders",
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        canDelete: false,
        reason: "Failed to check deletion eligibility",
      });
    }
  });

  // Delete address
  fastify.delete<{ Params: AddressParams }>("/addresses/:id", {
    preHandler: fastify.authenticate,
    schema: {
      params: {
        type: "object",
        required: ["id"],
        properties: {
          id: { type: "string", format: "uuid" },
        },
      },
      response: {
        200: {
          type: "object",
          properties: {
            success: { type: "boolean" },
            message: { type: "string" },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      const deleted = await deleteCustomer(request.userId!, id);

      if (!deleted) {
        return reply.status(404).send({
          success: false,
          message: "Address not found",
        });
      }

      return reply.send({
        success: true,
        message: "Address deleted successfully",
      });
    } catch (error: any) {
      fastify.log.error(error);
      
      if (error.message === "Cannot delete customer with existing orders") {
        return reply.status(400).send({
          success: false,
          message: error.message,
        });
      }

      return reply.status(500).send({
        success: false,
        message: "Failed to delete address",
      });
    }
  });

  // Export addresses as CSV
  fastify.get<{ Querystring: QueryParams }>("/addresses/export/csv", {
    preHandler: fastify.authenticate,
    schema: {
      querystring: {
        type: "object",
        properties: {
          type: { type: "string", enum: Object.values(AddressType) },
          search: { type: "string" },
          name: { type: "string" },
          phone: { type: "string" },
          cityName: { type: "string" },
          postalCode: { type: "string" },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const {
        type = AddressType.CUSTOMER,
        search,
        name,
        phone,
        cityName,
        postalCode,
      } = request.query;

      if (type !== AddressType.CUSTOMER) {
        return reply.status(400).send({
          success: false,
          message: "Only CUSTOMER type addresses are supported",
        });
      }

      const filters = { search, name, phone, cityName, postalCode };
      const csv = await exportCustomersCSV(request.userId!, filters);

      const filename = `customers-${new Date().toISOString().split("T")[0]}.csv`;

      return reply
        .header("Content-Type", "text/csv")
        .header("Content-Disposition", `attachment; filename="${filename}"`)
        .send(csv);
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        message: "Failed to export addresses",
      });
    }
  });
}