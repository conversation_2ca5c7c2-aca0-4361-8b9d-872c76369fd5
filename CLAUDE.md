# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

ShipTag is a full-stack application with a Next.js frontend and Fastify backend, implementing email-based OTP authentication with BetterAuth.

---

## 📄 Coding Style Guide

### Stack: Prisma + Fastify + TypeScript + Next.js

#### 🔤 Naming Conventions

* `camelCase` → variables, functions, fields, object keys (Prisma + DB included)
* `PascalCase` → types, interfaces, classes, React components, Prisma models
* `kebab-case` → file & folder names

#### 🗄️ Prisma

* Models: `PascalCase` (e.g., `User`, `OrderItem`)
* Fields: `camelCase` (e.g., `email`, `createdAt`)
* Database Fields: Also `camelCase` (no `snake_case`, no `@map()`)
* Stick to one naming convention across Prisma, database, and client

#### 🚀 Backend (Fastify + TypeScript)

* Organize by feature (`routes/user.ts`, `services/user-service.ts`, etc.)
* Route handlers must be thin (no business logic inside)
* Use Zod or DTOs for validation
* Use early returns, avoid deep nesting
* Type everything (no `any`)
* Use `const` by default
* Magic values must be extracted to constants or `.env`

#### 🖥️ Frontend (Next.js)

* Components: `PascalCase` filenames and names (e.g., `UserCard.tsx`)
* Hooks: `camelCase` with `use` prefix (`useUserAuth`)
* Pages: `kebab-case` filenames (e.g., `order-details.tsx`)
* Logic goes into hooks or utils; components must be pure/presentational

#### ✍️ Style & Standards

* Use Prettier for formatting
* Use ESLint (`strict` mode enabled)
* Comment "why", not "what"
* One export per file
* Prefer `async/await` over `.then()`
* Avoid premature abstraction

#### 🧪 Testing & Validation

* Use Zod or equivalent
* Test APIs/services with integration tests
* Test components with React Testing Library

#### 🔄 Naming Alignment Reference

| Concept         | Prisma (DB + Model) | Backend (TS)      | Frontend (Next.js) |
| --------------- | ------------------- | ----------------- | ------------------ |
| User model      | `User`              | `getUserById()`   | `useUser()`        |
| Product listing | `Product[]`         | `fetchProducts()` | `ProductList`      |
| Auth payload    | `userPayload`       | `UserPayload`     | `useAuth()`        |
| ID fields       | `userId`            | `userId`          | `user.id`          |

---

## Development Commands

### Backend (in `/backend`)

```bash
npm run dev              # Start development server (port 8080)
npm run build           # Compile TypeScript
npm run start           # Run production server
npm run prisma:generate # Generate Prisma client after schema changes
npm run prisma:migrate  # Apply database migrations
npm run prisma:studio   # Open Prisma Studio GUI
```

### Frontend (in `/frontend`)

```bash
npm run dev    # Start development server with Turbopack (port 3001)
npm run build  # Build for production
npm run start  # Run production server
npm run lint   # Run ESLint
```

## Architecture Overview

### Backend Architecture

The backend uses a layered architecture with clear separation of concerns:

1. **Entry Point**: `src/server.ts` bootstraps Fastify with plugins and routes
2. **Plugins** (`src/plugins/`): Fastify plugins for cross-cutting concerns

   * `prisma.ts`: Database connection management
3. **Routes** (`src/routes/`): HTTP endpoint definitions with schema validation

   * `auth.routes.ts`: Authentication endpoints with Swagger documentation
   * `address.routes.ts`: Customer address management endpoints
   * `location.routes.ts`: Location data endpoints for countries/states/cities
4. **Services** (`src/services/`): Business logic layer

   * `auth.service.ts`: Handles OTP generation, verification, and session management
   * `email.service.ts`: ReSend integration for sending emails
   * `address.service.ts`: CRUD operations for customer addresses
   * `location.service.ts`: Location data with caching
5. **Utils** (`src/utils/`): Shared utilities

   * `otp.ts`: OTP generation and expiration logic

### Authentication Flow

1. User requests OTP via `/api/auth/send-otp` with email
2. System generates 6-digit OTP, stores in database with 10-minute expiry
3. OTP sent via ReSend email service
4. User verifies OTP via `/api/auth/verify-otp`
5. System creates/retrieves user in BetterAuth and local database
6. Returns session token (7-day expiry)
7. Token used for authenticated requests via Authorization header

### Database Schema

* **User**: Stores user information including email and profile data
* **OtpSession**: Temporary OTP storage with verification tracking
* **AuthSession**: Stores authentication sessions with tokens and expiry
* **Address**: Multi-purpose address storage supporting CUSTOMER, SENDER, RECEIVER, BILLING, and COMPANY types
* **Country/State/City**: Location reference data

### Environment Configuration

Backend requires these environment variables:

* `DATABASE_URL`: PostgreSQL connection string
* `RESEND_API_KEY`: ReSend email service key
* `FRONTEND_URL`: Frontend URL for CORS configuration (default: http://localhost:3000)

### Key Dependencies

Backend:

* Fastify 5.x: High-performance web framework
* Prisma 6.x: Type-safe ORM
* better-auth: Authentication library with OTP support
* ReSend 4.x: Email delivery service

Frontend:

* Next.js 15.x: React framework with App Router
* Tailwind CSS 4.x: Utility-first CSS framework
* TypeScript 5.x: Type safety
* React Query 5.x: Server state management and data fetching
* Shadcn/ui: Component library built on Radix UI
* React Hook Form 7.x + Zod: Form handling and validation

### API Documentation

Swagger UI available at `http://************:8080/documentation` when backend is running.

## Customer Management Feature

The customer management system allows users to manage their customer database with the following features:

### Backend Implementation

* **Address Service**: CRUD operations for addresses with type='CUSTOMER'
  * Pagination with configurable page size (default 20, max 100)
  * Global search across name, phone, address fields
  * Column-specific filtering
  * CSV export functionality
  * Soft delete protection for customers with orders

* **Location Service**: Cached location data for countries, states, and cities
  * 5-minute cache TTL for performance
  * Hierarchical data structure (country → state → city)

### Frontend Implementation

* **Customer List Page** (`/customers`): 
  * Searchable and filterable data table
  * CSV export
  * Add/Edit/Delete actions
  * Integration with app sidebar

* **Customer Detail Page** (`/customers/[id]`):
  * Customer information display
  * Three tabs: Orders, Timeline, Statistics (mocked data)
  * Edit and delete actions

* **Modals and Forms**:
  * `CustomerForm`: Reusable form with dynamic location dropdowns
  * `AddCustomerModal`: Sheet-based modal for new customers
  * `EditCustomerModal`: Sheet-based modal for editing
  * `DeleteCustomerDialog`: Confirmation dialog with order check

### React Query Hooks

* `useCustomers`: List with pagination and filtering
* `useCustomer`: Single customer details
* `useCreateCustomer`: Create mutation
* `useUpdateCustomer`: Update mutation  
* `useDeleteCustomer`: Delete mutation
* `useExportCustomersCSV`: CSV export
* `useCountries/States/Cities`: Location data

### Key Design Decisions

1. **Address Type Reuse**: Customers are stored in the Address table with type='CUSTOMER' to enable future order/shipping integration
2. **Optimistic Updates**: React Query configured for optimistic UI updates
3. **Error Boundaries**: Component-level error handling for resilience
4. **Loading States**: Skeleton loaders for better UX
5. **Form Validation**: Zod schemas ensure data integrity
