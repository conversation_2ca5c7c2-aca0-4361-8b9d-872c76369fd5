{"permissions": {"allow": ["Bash(npm init:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx prisma:*)", "Bash(npm run prisma:generate:*)", "Bash(npm run prisma:seed:*)", "Bash(tsx check-seed.ts:*)", "Bash(npx tsx:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(npx:*)", "Bash(npm run prisma:migrate:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm run lint)", "<PERSON><PERSON>(true)", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(hostname:*)"], "deny": []}}