# Implementation Plan

- [x] 1. Set up backend API foundation for customer management
  - Create customer service layer with CRUD operations for Address table with type='Customer'
  - Implement user scoping and validation logic for all customer operations
  - Add error handling for business rules (prevent deletion of customers with shipments)
  - _Requirements: 7.1, 7.2, 7.3, 8.1, 8.2_

- [x] 2. Implement customer data retrieval and filtering APIs
  - Create GET /addresses endpoint with type=Customer filtering, search, and pagination support
  - Add query parameter handling for name, phone, city, zipCode filters with case-insensitive matching
  - Implement customer detail endpoint GET /addresses/:id with user scope validation
  - _Requirements: 1.1, 1.2, 1.3, 5.1, 7.4_

- [x] 3. Build customer creation and modification APIs
  - Implement POST /addresses endpoint for creating customers with type='Customer'
  - Add PUT /addresses/:id endpoint for updating customer records
  - Create comprehensive validation for all customer fields (name min 3 chars, international phone format)
  - _Requirements: 2.7, 2.8, 2.9, 3.4, 3.3_

- [x] 4. Implement customer deletion with business logic protection
  - Create DELETE /addresses/:id endpoint with shipment dependency checking
  - Add GET /addresses/:id/can-delete endpoint to check deletion eligibility
  - Implement proper error responses for customers linked to shipments
  - _Requirements: 4.3, 4.4, 8.1, 8.2_

- [x] 5. Create location data APIs for dynamic dropdowns
  - Implement GET /countries endpoint returning available countries
  - Create GET /states endpoint with country filtering for dynamic state population
  - Add GET /cities endpoint with country and state filtering for dynamic city population
  - _Requirements: 2.4, 2.5, 2.6_

- [-] 6. Build customer list page with search and filtering
  - Create customers page component at /customers with table layout
  - Implement global search bar with real-time filtering by name, phone, and address
  - Add column-specific typeahead filters for Name, Phone, City, and Zip columns
  - Create pagination controls with proper state management
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [ ] 7. Implement customer table with actions and data display
  - Create CustomerTable component displaying Name, Phone, Address, Date Added, Orders, Actions columns
  - Add action buttons for Edit, Delete, and View Detail on each row
  - Implement empty state display when no customers exist
  - Add loading states and error handling for data fetching
  - _Requirements: 1.4, 1.5_

- [ ] 8. Create customer side modal for adding new customers
  - Build CustomerModal component as side drawer with "Add Customer" title
  - Implement customer form with all required fields: Name, Phone, Country, State, City, Address 1, Postal Code
  - Add Address 2 as optional field with proper form layout
  - Create form validation with real-time error display
  - _Requirements: 2.1, 2.2, 2.8, 2.9_

- [ ] 9. Implement dynamic dropdown components with typeahead functionality
  - Create CountryDropdown component with search/typeahead capability
  - Build StateDropdown that dynamically populates based on selected country
  - Implement CityDropdown that updates based on selected state and country
  - Add PhoneInput component with country code dropdown for international format
  - _Requirements: 2.3, 2.4, 2.5, 2.6_

- [ ] 10. Complete customer modal functionality and integration
  - Wire up form submission to POST /addresses API with proper error handling
  - Implement modal close behavior and table refresh after successful creation
  - Add form reset and validation state management
  - Create success/error toast notifications for user feedback
  - _Requirements: 2.7, 2.10_

- [ ] 11. Build customer edit functionality
  - Modify CustomerModal to support edit mode with "Edit Customer" title
  - Implement data pre-filling when modal opens in edit mode
  - Wire up form submission to PUT /addresses/:id API
  - Add proper state management for edit vs create modes
  - _Requirements: 3.1, 3.2, 3.5_

- [ ] 12. Create delete confirmation dialog and logic
  - Build DeleteConfirmationDialog component with specified warning message
  - Implement delete button click handler with confirmation flow
  - Add API integration to check deletion eligibility before showing dialog
  - Create error handling for customers with linked shipments
  - _Requirements: 4.1, 4.2, 4.4, 4.5_

- [ ] 13. Implement customer detail page with profile display
  - Create customer detail page at /customers/[id] route
  - Build CustomerProfile component displaying complete customer information
  - Add Edit and Delete buttons with proper modal integration
  - Implement "Back to Customer List" navigation button
  - _Requirements: 5.1, 5.2, 5.3, 5.6_

- [ ] 14. Build shipment history integration for customer details
  - Create ShipmentHistoryTable component with specified columns (Shipment ID, Status, Carrier, Date, Amount, Actions)
  - Implement GET /shipments?addressId=:customerId API integration
  - Add search and filter controls for shipment history table
  - Create proper loading and empty states for shipment data
  - _Requirements: 5.4, 5.5, 8.3_

- [ ] 15. Implement CSV export functionality
  - Create export button component with proper styling and placement
  - Implement CSV generation logic that respects current filter state
  - Add API endpoint or client-side logic for CSV data formatting
  - Include all relevant customer fields in exported CSV file
  - _Requirements: 6.1, 6.2, 6.3_

- [ ] 16. Add comprehensive error handling and user feedback
  - Implement toast notification system for success/error messages
  - Add proper loading states throughout the application
  - Create error boundaries for component-level error handling
  - Add retry mechanisms for failed API calls
  - _Requirements: 4.4, 8.2_

- [ ] 17. Create comprehensive test suite for customer functionality
  - Write unit tests for customer service layer and validation logic
  - Create component tests for all customer management components
  - Implement integration tests for complete customer CRUD workflows
  - Add API endpoint tests for all customer-related endpoints
  - _Requirements: All requirements validation_

- [ ] 18. Integrate customer management with existing application structure
  - Add customer management routes to Next.js app router structure
  - Integrate with existing authentication and user context
  - Connect to existing Prisma database configuration
  - Ensure proper styling consistency with existing UI components
  - _Requirements: 7.1, 7.2, 7.3_