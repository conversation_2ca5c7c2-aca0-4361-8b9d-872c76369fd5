# Requirements Document

## Introduction

The Customer Management System enables ShipTag users to efficiently manage customer records containing contact information and addresses. These customer records are used for quick selection when booking shipments and are stored in the Address table with type = 'Customer'. The system provides comprehensive CRUD operations, search and filtering capabilities, and integration with shipment tracking.

## Requirements

### Requirement 1

**User Story:** As a ShipTag user, I want to view and manage all my saved customers in a searchable list, so that I can quickly find and work with customer information.

#### Acceptance Criteria

1. WHEN I navigate to the customers page THEN the system SHALL display a paginated list of all my customers
2. WHEN I use the global search bar THEN the system SHALL filter customers by name, phone, or address in real-time
3. WHEN I apply column filters THEN the system SHALL provide typeahead filtering for Name, Phone, City, and Zip columns
4. WHEN I view the customer table THEN the system SHALL display columns for Name, Phone, Address, Date Added, Orders, and Actions
5. IF I have no customers THEN the system SHALL display an appropriate empty state message

### Requirement 2

**User Story:** As a ShipTag user, I want to add new customers through a side modal, so that I can quickly capture complete customer information without leaving the current page.

#### Acceptance Criteria

1. WHEN I click "Add New Customer" THEN the system SHALL open a side modal with the title "Add Customer"
2. WHEN I fill out the customer form THEN the system SHALL require Name, Phone, Country, State, City, Address 1, and Postal Code
3. WHEN I interact with the Phone field THEN the system SHALL provide a country dropdown for international phone format selection
4. WHEN I select a country THEN the system SHALL dynamically populate the state dropdown with relevant options based on the selected country
5. WHEN I select a state THEN the system SHALL dynamically populate the city dropdown with relevant options based on the selected state
6. WHEN I use any dropdown field THEN the system SHALL provide typeahead/autocomplete functionality for easy selection
7. WHEN I submit valid customer data THEN the system SHALL create the customer record with type = 'Customer'
8. WHEN I submit the form THEN the system SHALL validate that Name has minimum 3 characters
9. WHEN I submit the form THEN the system SHALL validate that Phone is in international format
10. WHEN I save successfully THEN the system SHALL refresh the customer table and close the modal

### Requirement 3

**User Story:** As a ShipTag user, I want to edit existing customer information, so that I can keep customer records up to date.

#### Acceptance Criteria

1. WHEN I click edit on a customer THEN the system SHALL open a side modal with the title "Edit Customer"
2. WHEN the edit modal opens THEN the system SHALL pre-fill all fields with existing customer data
3. WHEN I modify customer information THEN the system SHALL apply the same validation rules as adding a customer
4. WHEN I save changes THEN the system SHALL update the customer record via PUT /addresses/:id
5. WHEN I save successfully THEN the system SHALL refresh the customer table and close the modal

### Requirement 4

**User Story:** As a ShipTag user, I want to delete customers that are no longer needed, so that I can maintain a clean customer list.

#### Acceptance Criteria

1. WHEN I click delete on a customer THEN the system SHALL show a confirmation dialog
2. WHEN the confirmation dialog appears THEN the system SHALL display "Are you sure you want to delete this customer? This action cannot be undone."
3. WHEN I confirm deletion AND the customer has no linked shipments THEN the system SHALL delete the customer record
4. WHEN I attempt to delete a customer with linked shipments THEN the system SHALL prevent deletion and show an error message
5. WHEN deletion is successful THEN the system SHALL refresh the customer table

### Requirement 5

**User Story:** As a ShipTag user, I want to view detailed information about a specific customer and their shipment history, so that I can understand the complete customer relationship.

#### Acceptance Criteria

1. WHEN I click on a customer name THEN the system SHALL navigate to a detailed customer view page
2. WHEN I view customer details THEN the system SHALL display complete customer profile information including Name, Phone, and Full Address
3. WHEN I view customer details THEN the system SHALL show Edit and Delete buttons for customer management
4. WHEN I view customer details THEN the system SHALL display a shipment history table with columns for Shipment ID, Status, Carrier, Date, Amount, and Actions
5. WHEN I view shipment history THEN the system SHALL provide filter and search controls for the shipment table
6. WHEN I want to return THEN the system SHALL provide a "Back to Customer List" button

### Requirement 6

**User Story:** As a ShipTag user, I want to export my customer data to CSV, so that I can use the information in external systems or for reporting.

#### Acceptance Criteria

1. WHEN I click "Export CSV" THEN the system SHALL export all currently filtered customer records
2. WHEN I export customers THEN the system SHALL include all relevant customer fields in the CSV file
3. WHEN filters are applied THEN the system SHALL only export customers matching the current filter criteria

### Requirement 7

**User Story:** As a ShipTag user, I want all customer operations to be secure and scoped to my account, so that I can only access and modify my own customer data.

#### Acceptance Criteria

1. WHEN I perform any customer operation THEN the system SHALL scope all operations by my userId
2. WHEN I view customers THEN the system SHALL only show Address records where type = 'Customer' and userId matches mine
3. WHEN I create, edit, or delete customers THEN the system SHALL ensure the operation is limited to my user scope
4. WHEN I access customer details THEN the system SHALL verify I own the customer record before displaying information

### Requirement 8

**User Story:** As a ShipTag user, I want the system to maintain data integrity between customers and shipments, so that I cannot accidentally break shipment references.

#### Acceptance Criteria

1. WHEN a customer is linked to shipments THEN the system SHALL prevent deletion of that customer
2. WHEN I attempt to delete a linked customer THEN the system SHALL display an appropriate error message
3. WHEN I view customer details THEN the system SHALL accurately display the count of linked orders/shipments
4. WHEN customer data is updated THEN the system SHALL maintain referential integrity with existing shipments