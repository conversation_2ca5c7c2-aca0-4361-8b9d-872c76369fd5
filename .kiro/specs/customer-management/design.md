# Design Document

## Overview

The Customer Management System is a comprehensive solution for managing customer records within ShipTag. The system leverages the existing Address table with type = 'Customer' to store customer data, providing a unified approach to address management across the platform. The design emphasizes user experience through responsive interfaces, real-time search capabilities, and seamless integration with the shipment system.

## Architecture

### Frontend Architecture
- **Framework**: Next.js 15 with App Router
- **Pages**: 
  - `/customers` - Customer list view
  - `/customers/[id]` - Customer detail view
- **Components**: Modular React components with TypeScript
- **State Management**: React hooks and context for local state
- **UI Library**: shadcn/ui components with Radix UI primitives

### Backend Architecture
- **API Layer**: Fastify routes with TypeScript validation
- **Data Layer**: Prisma ORM with PostgreSQL
- **Authentication**: BetterAuth integration for user scoping
- **Business Logic**: Service layer for customer operations

### Data Flow
```
Frontend Components → API Client → Fastify Routes → Services → Prisma → PostgreSQL
```

## Components and Interfaces

### 1. Customer List Page (`/customers`)

**Layout Structure:**
```
┌─────────────────────────────────────────┐
│ Page Title: "Customers"                 │
├─────────────────────────────────────────┤
│ [Global Search] [Add Customer] [Export] │
├─────────────────────────────────────────┤
│ Column Filters Row                      │
├─────────────────────────────────────────┤
│ Customer Table                          │
│ ┌─────┬───────┬─────────┬──────┬───────┐│
│ │Name │ Phone │ Address │ Date │Actions││
│ └─────┴───────┴─────────┴──────┴───────┘│
├─────────────────────────────────────────┤
│ Pagination Controls                     │
└─────────────────────────────────────────┘
```

**Key Components:**
- `CustomerListPage` - Main page component
- `CustomerTable` - Data table with sorting and filtering
- `GlobalSearchBar` - Real-time search input
- `ColumnFilters` - Typeahead filters for each column
- `CustomerTableRow` - Individual row with actions
- `PaginationControls` - Page navigation

### 2. Customer Side Modal (Add/Edit)

**Layout Structure:**
```
┌─────────────────────────────────┐
│ [X] Add/Edit Customer           │
├─────────────────────────────────┤
│ Name: [________________]        │
│ Phone: [Country▼][_________]    │
│ Country: [Dropdown with search] │
│ State: [Dropdown with search]   │
│ City: [Dropdown with search]    │
│ Address 1: [_______________]    │
│ Address 2: [_______________]    │
│ Postal Code: [_____________]    │
├─────────────────────────────────┤
│           [Cancel] [Save]       │
└─────────────────────────────────┘
```

**Key Components:**
- `CustomerModal` - Side drawer container
- `CustomerForm` - Form with validation
- `CountryDropdown` - Searchable country selector
- `StateDropdown` - Dynamic state selector
- `CityDropdown` - Dynamic city selector
- `PhoneInput` - International phone input with country code

### 3. Customer Detail Page (`/customers/[id]`)

**Layout Structure:**
```
┌─────────────────────────────────────────┐
│ [← Back to Customers]                   │
├─────────────────────────────────────────┤
│ Customer Profile                        │
│ Name: John Doe        [Edit] [Delete]   │
│ Phone: ******-0123                      │
│ Address: 123 Main St, City, State       │
├─────────────────────────────────────────┤
│ Shipment History                        │
│ [Search] [Filters]                      │
│ ┌────────┬────────┬─────────┬──────────┐│
│ │Ship ID │ Status │ Carrier │   Date   ││
│ └────────┴────────┴─────────┴──────────┘│
└─────────────────────────────────────────┘
```

**Key Components:**
- `CustomerDetailPage` - Main detail view
- `CustomerProfile` - Customer information display
- `ShipmentHistoryTable` - Related shipments table
- `BackButton` - Navigation back to list

### 4. Delete Confirmation Dialog

**Key Components:**
- `DeleteConfirmationDialog` - Modal confirmation
- Standard dialog with warning message and action buttons

## Data Models

### Address Model (Existing)
```typescript
interface Address {
  id: string;
  userId: string;
  type: string; // 'Customer' for customer records
  name: string;
  phone: string;
  country: string;
  state: string;
  city: string;
  street1: string;
  street2?: string;
  zipCode?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Frontend Types
```typescript
interface Customer {
  id: string;
  name: string;
  phone: string;
  country: string;
  state: string;
  city: string;
  street1: string;
  street2?: string;
  zipCode?: string;
  createdAt: Date;
  updatedAt: Date;
  orderCount?: number;
}

interface CustomerFormData {
  name: string;
  phone: string;
  country: string;
  state: string;
  city: string;
  street1: string;
  street2?: string;
  zipCode?: string;
}

interface CustomerFilters {
  search?: string;
  name?: string;
  phone?: string;
  city?: string;
  zipCode?: string;
}

interface PaginationParams {
  page: number;
  limit: number;
}
```

### API Response Types
```typescript
interface CustomerListResponse {
  customers: Customer[];
  total: number;
  page: number;
  limit: number;
}

interface CountryOption {
  code: string;
  name: string;
}

interface StateOption {
  code: string;
  name: string;
  countryCode: string;
}

interface CityOption {
  name: string;
  stateCode: string;
  countryCode: string;
}
```

## API Design

### Customer Operations
```typescript
// Get customers with filtering and pagination
GET /addresses?type=Customer&search=&name=&phone=&city=&zipCode=&page=1&limit=20
Response: CustomerListResponse

// Get single customer
GET /addresses/:id
Response: Customer

// Create customer
POST /addresses
Body: CustomerFormData & { type: 'Customer' }
Response: Customer

// Update customer
PUT /addresses/:id
Body: CustomerFormData
Response: Customer

// Delete customer
DELETE /addresses/:id
Response: { success: boolean }
```

### Location Data Operations
```typescript
// Get countries
GET /countries
Response: CountryOption[]

// Get states by country
GET /states?country=US
Response: StateOption[]

// Get cities by state and country
GET /cities?country=US&state=CA
Response: CityOption[]
```

### Shipment Integration
```typescript
// Get shipments for customer
GET /shipments?addressId=:customerId
Response: Shipment[]

// Check if customer can be deleted
GET /addresses/:id/can-delete
Response: { canDelete: boolean, reason?: string }
```

## Error Handling

### Frontend Error Handling
- **Form Validation**: Real-time validation with error messages
- **API Errors**: Toast notifications for user feedback
- **Network Errors**: Retry mechanisms and offline indicators
- **Loading States**: Skeleton loaders and spinners

### Backend Error Handling
```typescript
// Validation errors
400 Bad Request: { error: "Validation failed", details: ValidationError[] }

// Authorization errors
401 Unauthorized: { error: "Authentication required" }
403 Forbidden: { error: "Access denied" }

// Business logic errors
409 Conflict: { error: "Cannot delete customer with active shipments" }

// Server errors
500 Internal Server Error: { error: "Internal server error" }
```

### Error Recovery Strategies
- **Optimistic Updates**: Revert on failure
- **Retry Logic**: Automatic retry for transient failures
- **Graceful Degradation**: Fallback to basic functionality
- **User Feedback**: Clear error messages with suggested actions

## Testing Strategy

### Frontend Testing
```typescript
// Component Testing (React Testing Library + Jest)
- CustomerTable component rendering and interactions
- CustomerModal form validation and submission
- Search and filter functionality
- Pagination controls

// Integration Testing
- Customer CRUD operations end-to-end
- Modal open/close workflows
- Search and filter integration
- Navigation between pages

// E2E Testing (Playwright)
- Complete customer management workflows
- Cross-browser compatibility
- Mobile responsiveness
```

### Backend Testing
```typescript
// Unit Testing (Jest)
- Customer service methods
- Validation logic
- Business rule enforcement
- Error handling

// Integration Testing
- API endpoint functionality
- Database operations
- Authentication middleware
- Location service integration

// API Testing
- Request/response validation
- Error scenarios
- Performance testing
- Security testing
```

### Test Data Management
- **Fixtures**: Predefined test customers
- **Factories**: Dynamic test data generation
- **Cleanup**: Automated test data cleanup
- **Isolation**: Test database separation

## Performance Considerations

### Frontend Optimization
- **Code Splitting**: Lazy load customer detail pages
- **Memoization**: React.memo for table rows
- **Virtual Scrolling**: For large customer lists
- **Debounced Search**: Reduce API calls during typing

### Backend Optimization
- **Database Indexing**: Indexes on userId, type, and search fields
- **Query Optimization**: Efficient filtering and pagination
- **Caching**: Redis cache for location data
- **Rate Limiting**: Prevent API abuse

### Data Loading Strategies
- **Pagination**: Server-side pagination for large datasets
- **Prefetching**: Preload next page data
- **Incremental Loading**: Load additional data on scroll
- **Background Sync**: Update data in background

## Security Considerations

### Authentication & Authorization
- **User Scoping**: All operations filtered by userId
- **JWT Validation**: Verify user tokens on all requests
- **Role-Based Access**: Future-proof for admin roles
- **Session Management**: Secure session handling

### Data Protection
- **Input Sanitization**: Prevent XSS and injection attacks
- **Data Validation**: Server-side validation for all inputs
- **Audit Logging**: Track customer data changes
- **GDPR Compliance**: Data export and deletion capabilities

### API Security
- **Rate Limiting**: Prevent brute force attacks
- **CORS Configuration**: Restrict cross-origin requests
- **Request Validation**: Validate all API inputs
- **Error Information**: Limit error details in responses