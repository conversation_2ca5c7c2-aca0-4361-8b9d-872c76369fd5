# Project Structure

## Root Level
```
├── frontend/           # Next.js React application
├── backend/            # Fastify API server
├── tasks/              # Project task documentation
└── .kiro/              # Kiro AI assistant configuration
```

## Frontend Structure (`frontend/`)
```
├── app/                # Next.js App Router pages
│   ├── (authenticated)/ # Protected route group
│   ├── dashboard/      # Dashboard pages
│   ├── login/          # Authentication pages
│   └── layout.tsx      # Root layout with theme provider
├── components/         # Reusable React components
│   ├── apps/           # App marketplace components
│   ├── customers/      # Customer management components
│   └── ui/             # shadcn/ui base components
├── lib/                # Utility libraries
│   ├── api/            # API client functions
│   ├── types/          # TypeScript type definitions
│   └── utils/          # Helper functions
├── hooks/              # Custom React hooks
└── public/             # Static assets
```

## Backend Structure (`backend/`)
```
├── src/
│   ├── auth/           # Authentication logic
│   ├── plugins/        # Fastify plugins (auth, prisma)
│   ├── routes/         # API route handlers
│   ├── services/       # Business logic services
│   ├── utils/          # Utility functions
│   └── server.ts       # Main server entry point
├── prisma/
│   ├── migrations/     # Database migration files
│   ├── schema.prisma   # Database schema definition
│   └── seed.ts         # Database seeding script
└── package.json        # Dependencies and scripts
```

## Key Conventions

### File Naming
- Use kebab-case for component files: `apps-settings-page.tsx`
- Use camelCase for utility files: `useAuth.ts`
- Use PascalCase for type definitions: `App.types.ts`

### Component Organization
- Group related components in feature folders (`apps/`, `customers/`)
- Keep UI primitives in `components/ui/`
- Export components from index files for clean imports

### API Structure
- Organize routes by feature domain
- Use services for business logic
- Keep database operations in Prisma service layer

### Database Schema
- Use PascalCase for model names
- Use camelCase for field names
- Organize models by functional domains with comments