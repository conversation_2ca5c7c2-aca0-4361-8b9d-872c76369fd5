# Technology Stack

## Frontend
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 4
- **UI Components**: Radix UI primitives with shadcn/ui
- **Icons**: Lucide React
- **Theming**: next-themes for dark/light mode
- **State Management**: React hooks and context

## Backend
- **Framework**: Fastify 5 with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: BetterAuth with JWT tokens
- **Email**: Resend for OTP delivery
- **Documentation**: Swagger/OpenAPI
- **Validation**: Built-in Fastify validation

## Development Tools
- **Package Manager**: npm
- **Build Tools**: Next.js built-in bundler, TypeScript compiler
- **Linting**: ESLint with Next.js config
- **Database Migrations**: Prisma migrate

## Common Commands

### Frontend Development
```bash
cd frontend
npm run dev          # Start development server with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Backend Development
```bash
cd backend
npm run dev          # Start development server with nodemon
npm run build        # Compile TypeScript
npm run start        # Start production server
```

### Database Operations
```bash
cd backend
npm run prisma:generate  # Generate Prisma client
npm run prisma:migrate   # Run database migrations
npm run prisma:studio    # Open Prisma Studio
npm run prisma:seed      # Seed database
```

## Code Style Conventions
- Use TypeScript strict mode
- Prefer functional components with hooks
- Use Tailwind utility classes for styling
- Follow Next.js App Router conventions
- Use Prisma for all database operations
- Implement proper error handling with try/catch