import { SmsAlertsResponse, SmsAlertsQueryParams, SmsAlertsFilters, SmsAlertsPagination } from "../types/sms-alerts";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

// Helper function to build query string
function buildQueryString(params: Partial<SmsAlertsQueryParams>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      searchParams.append(key, String(value));
    }
  });
  
  return searchParams.toString();
}

// Helper function to get auth token from localStorage
function getAuthToken(): string | null {
  if (typeof window === "undefined") return null;
  return localStorage.getItem("authToken");
}

// Helper function to make authenticated API requests
async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const token = getAuthToken();
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

/**
 * Fetch SMS alerts with optional filtering and pagination
 */
export async function getSmsAlerts(
  filters: SmsAlertsFilters = {},
  pagination: SmsAlertsPagination = { page: 1, limit: 20 }
): Promise<SmsAlertsResponse> {
  const params = { ...filters, ...pagination };
  const queryString = buildQueryString(params);
  const endpoint = `/api/sms-alerts${queryString ? `?${queryString}` : ""}`;

  return apiRequest<SmsAlertsResponse>(endpoint);
}
