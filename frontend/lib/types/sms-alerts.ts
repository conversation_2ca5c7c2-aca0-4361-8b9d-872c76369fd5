export interface SmsAlert {
  id: string;
  message: string;
  triggerType: string;
  language: string;
  status: "Active" | "Inactive";
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SmsAlertsFilters {
  search?: string;
  triggerType?: string;
  language?: string;
  status?: string;
}

export interface SmsAlertsPagination {
  page: number;
  limit: number;
}

export interface SmsAlertsResponse {
  success: boolean;
  data: SmsAlert[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SmsAlertsQueryParams extends SmsAlertsFilters, SmsAlertsPagination {}

// Filter options for dropdowns
export interface FilterOption {
  value: string;
  label: string;
}

export const TRIGGER_TYPE_OPTIONS: FilterOption[] = [
  { value: "Tracking", label: "Tracking" },
  { value: "Delivered", label: "Delivered" },
  { value: "Failed", label: "Failed" },
  { value: "Out for Delivery", label: "Out for Delivery" },
  { value: "Testing", label: "Testing" },
];

export const LANGUAGE_OPTIONS: FilterOption[] = [
  { value: "English", label: "English" },
  { value: "Arabic", label: "Arabic" },
];

export const STATUS_OPTIONS: FilterOption[] = [
  { value: "Active", label: "Active" },
  { value: "Inactive", label: "Inactive" },
];

// Supported variables in SMS templates
export const SMS_VARIABLES = [
  {
    placeholder: "{{deliveryCompany}}",
    description: "The name of the shipping carrier",
  },
  {
    placeholder: "{{trackingNumber}}",
    description: "The package tracking number",
  },
  {
    placeholder: "{{link}}",
    description: "A link to the tracking page",
  },
] as const;
