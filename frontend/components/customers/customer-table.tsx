"use client";

import { useState } from "react";
import { Edit, Trash2, Eye, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { type Customer, type CustomerFilters } from "@/lib/api/customers";
import Link from "next/link";

interface CustomerTableProps {
  customers: Customer[];
  loading: boolean;
  onEdit: (customer: Customer) => void;
  onDelete: (customer: Customer) => void;
  onFilterChange: (filters: CustomerFilters) => void;
  filters: CustomerFilters;
  page: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function CustomerTable({
  customers,
  loading,
  onEdit,
  onDelete,
  onFilterChange,
  filters,
  page,
  totalPages,
  onPageChange,
}: CustomerTableProps) {
  const [localFilters, setLocalFilters] = useState<CustomerFilters>(filters);

  const handleFilterChange = (field: keyof CustomerFilters, value: string) => {
    const newFilters = { ...localFilters, [field]: value || undefined };
    setLocalFilters(newFilters);
    onFilterChange(newFilters);
  };

  const formatAddress = (customer: Customer) => {
    const parts = [
      customer.address1,
      customer.cityName,
      customer.state,
      customer.postalCode,
    ].filter(Boolean);
    return parts.join(", ");
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-10" />
          ))}
        </div>
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-12 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (customers.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-muted-foreground text-lg mb-2">No customers found</div>
        <div className="text-sm text-muted-foreground">
          {Object.values(filters).some(Boolean) || filters.search
            ? "Try adjusting your search or filters"
            : "Add your first customer to get started"}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Column Filters */}
      <div className="grid grid-cols-4 gap-4">
        <Input
          placeholder="Filter by name..."
          value={localFilters.name || ""}
          onChange={(e) => handleFilterChange("name", e.target.value)}
        />
        <Input
          placeholder="Filter by phone..."
          value={localFilters.phone || ""}
          onChange={(e) => handleFilterChange("phone", e.target.value)}
        />
        <Input
          placeholder="Filter by city..."
          value={localFilters.cityName || ""}
          onChange={(e) => handleFilterChange("cityName", e.target.value)}
        />
        <Input
          placeholder="Filter by zip..."
          value={localFilters.postalCode || ""}
          onChange={(e) => handleFilterChange("postalCode", e.target.value)}
        />
      </div>

      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>Date Added</TableHead>
            <TableHead>Orders</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {customers.map((customer) => (
            <TableRow key={customer.id}>
              <TableCell className="font-medium">
                <Link
                  href={`/customers/${customer.id}`}
                  className="text-primary hover:underline"
                >
                  {customer.name || "—"}
                </Link>
              </TableCell>
              <TableCell>{customer.phone || "—"}</TableCell>
              <TableCell 