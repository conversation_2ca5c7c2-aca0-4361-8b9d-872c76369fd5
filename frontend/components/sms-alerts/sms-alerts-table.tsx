"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ev<PERSON><PERSON><PERSON>, MoreHorizontal } from "lucide-react";
import { <PERSON>ton } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { type SmsAlert } from "@/lib/types/sms-alerts";

interface SmsAlertsTableProps {
  smsAlerts: SmsAlert[];
  loading: boolean;
  page: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function SmsAlertsTable({
  smsAlerts,
  loading,
  page,
  totalPages,
  onPageChange,
}: SmsAlertsTableProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Message</TableHead>
              <TableHead>Trigger Type</TableHead>
              <TableHead>Language</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-4 w-[300px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[100px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[80px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[60px]" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-8 w-8 rounded-md ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (smsAlerts.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No SMS alerts found.</p>
      </div>
    );
  }

  const formatMessage = (message: string, isDefault: boolean) => {
    if (message.length <= 100) return message;
    
    const truncated = message.substring(0, 100) + "...";
    return truncated;
  };

  const getStatusBadge = (status: "Active" | "Inactive") => {
    return (
      <Badge variant={status === "Active" ? "default" : "secondary"}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* Table */}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Message</TableHead>
            <TableHead>Trigger Type</TableHead>
            <TableHead>Language</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {smsAlerts.map((alert) => (
            <TableRow 
              key={alert.id}
              className={alert.isDefault ? "bg-muted/30" : ""}
            >
              <TableCell className="max-w-[400px]">
                <div className="space-y-1">
                  <p className={`text-sm ${alert.isDefault ? "font-medium" : ""}`}>
                    {formatMessage(alert.message, alert.isDefault)}
                  </p>
                  {alert.isDefault && (
                    <Badge variant="outline" className="text-xs">
                      Default Message
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell>{alert.triggerType}</TableCell>
              <TableCell>{alert.language}</TableCell>
              <TableCell>{getStatusBadge(alert.status)}</TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      className="h-8 w-8 p-0"
                      disabled={alert.isDefault}
                    >
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem disabled>
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem disabled>
                      Edit Message
                    </DropdownMenuItem>
                    <DropdownMenuItem disabled>
                      Delete Message
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Page {page} of {totalPages}
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page - 1)}
              disabled={page <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page + 1)}
              disabled={page >= totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
