"use client";

import { useState, useEffect, useCallback } from "react";
import { Plus, Download, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CustomerTable } from "@/components/customers/customer-table";
import { CustomerModal } from "@/components/customers/customer-modal";
import { DeleteConfirmationDialog } from "@/components/customers/delete-confirmation-dialog";
import { 
  getCustomers, 
  exportCustomersCSV, 
  deleteCustomer,
  canDeleteCustomer,
  type Customer, 
  type CustomerFilters 
} from "@/lib/api/customers";

export default function CustomersPage() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [totalPages, setTotalPages] = useState(0);
  
  // Filters
  const [globalSearch, setGlobalSearch] = useState("");
  const [filters, setFilters] = useState<CustomerFilters>({});
  
  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  const fetchCustomers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const currentFilters = {
        ...filters,
        ...(globalSearch && { search: globalSearch }),
      };
      
      const response = await getCustomers(currentFilters, { page, limit });
      
      setCustomers(response.data);
      setTotal(response.total);
      setTotalPages(response.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch customers");
    } finally {
      setLoading(false);
    }
  }, [filters, globalSearch, page, limit]);

  useEffect(() => {
    fetchCustomers();
  }, [fetchCustomers]);

  const handleSearch = (value: string) => {
    setGlobalSearch(value);
    setPage(1); // Reset to first page when searching
  };

  const handleFilterChange = (newFilters: CustomerFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filtering
  };

  const handleAddCustomer = () => {
    setEditingCustomer(null);
    setIsModalOpen(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer);
    setIsModalOpen(true);
  };

  const handleDeleteCustomer = async (customer: Customer) => {
    try {
      // Check if customer can be deleted
      const canDelete = await canDeleteCustomer(customer.id);
      
      if (!canDelete.canDelete) {
        setDeleteError(canDelete.reason);
        setCustomerToDelete(customer);
        setDeleteDialogOpen(true);
        return;
      }
      
      setDeleteError(null);
      setCustomerToDelete(customer);
      setDeleteDialogOpen(true);
    } catch (err) {
      setDeleteError(err instanceof Error ? err.message : "Failed to check deletion eligibility");
      setCustomerToDelete(customer);
      setDeleteDialogOpen(true);
    }
  };

  const confirmDelete = async () => {
    if (!customerToDelete) return;
    
    try {
      await deleteCustomer(customerToDelete.id);
      setDeleteDialogOpen(false);
      setCustomerToDelete(null);
      setDeleteError(null);
      fetchCustomers(); // Refresh the list
    } catch (err) {
      setDeleteError(err instanceof Error ? err.message : "Failed to delete customer");
    }
  };

  const handleExportCSV = async () => {
    try {
      const currentFilters = {
        ...filters,
        ...(globalSearch && { search: globalSearch }),
      };
      
      const blob = await exportCustomersCSV(currentFilters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `customers-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to export CSV");
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingCustomer(null);
    fetchCustomers(); // Refresh the list
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Customers</h1>
        <div className="flex items-center gap-2">
          <Button onClick={handleExportCSV} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button onClick={handleAddCustomer}>
            <Plus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer Management</CardTitle>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search customers..."
                value={globalSearch}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-destructive/15 text-destructive px-4 py-2 rounded-md mb-4">
              {error}
            </div>
          )}
          
          <CustomerTable
            customers={customers}
            loading={loading}
            onEdit={handleEditCustomer}
            onDelete={handleDeleteCustomer}
            onFilterChange={handleFilterChange}
            filters={filters}
            page={page}
            totalPages={totalPages}
            onPageChange={setPage}
          />
        </CardContent>
      </Card>

      <CustomerModal
        open={isModalOpen}
        onClose={handleModalClose}
        customer={editingCustomer}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={() => {
          setDeleteDialogOpen(false);
          setCustomerToDelete(null);
          setDeleteError(null);
        }}
        onConfirm={confirmDelete}
        customerName={customerToDelete?.name || ""}
        error={deleteError}
        canDelete={!deleteError || !deleteError.includes("existing orders")}
      />
    </div>
  );
}