"use client";

import { useState, useEffect, useCallback } from "react";
import { Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SmsAlertsTable } from "@/components/sms-alerts/sms-alerts-table";
import { 
  getSmsAlerts,
  type SmsAlert, 
  type SmsAlertsFilters 
} from "@/lib/api/sms-alerts";
import { 
  TRIGGER_TYPE_OPTIONS, 
  LANGUAGE_OPTIONS, 
  STATUS_OPTIONS 
} from "@/lib/types/sms-alerts";

export default function SmsAlertsPage() {
  const [smsAlerts, setSmsAlerts] = useState<SmsAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [totalPages, setTotalPages] = useState(0);
  
  // Filters
  const [globalSearch, setGlobalSearch] = useState("");
  const [filters, setFilters] = useState<SmsAlertsFilters>({});

  const fetchSmsAlerts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const currentFilters = {
        ...filters,
        ...(globalSearch && { search: globalSearch }),
      };
      
      const response = await getSmsAlerts(currentFilters, { page, limit });
      
      setSmsAlerts(response.data);
      setTotal(response.total);
      setTotalPages(response.totalPages);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch SMS alerts");
    } finally {
      setLoading(false);
    }
  }, [filters, globalSearch, page, limit]);

  useEffect(() => {
    fetchSmsAlerts();
  }, [fetchSmsAlerts]);

  const handleSearch = (value: string) => {
    setGlobalSearch(value);
    setPage(1); // Reset to first page when searching
  };

  const handleFilterChange = (key: keyof SmsAlertsFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
    setPage(1); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilters({});
    setGlobalSearch("");
    setPage(1);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">SMS</h1>
        <p className="text-muted-foreground">
          View all SMS messages sent to your customers during shipment events.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Customer SMS</CardTitle>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            {/* Global Search */}
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search SMS messages..."
                value={globalSearch}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Filter Dropdowns */}
            <div className="flex gap-2">
              <Select
                value={filters.triggerType || ""}
                onValueChange={(value) => handleFilterChange("triggerType", value)}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Trigger Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Triggers</SelectItem>
                  {TRIGGER_TYPE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={filters.language || ""}
                onValueChange={(value) => handleFilterChange("language", value)}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Languages</SelectItem>
                  {LANGUAGE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={filters.status || ""}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  {STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {(Object.keys(filters).length > 0 || globalSearch) && (
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-destructive/15 text-destructive px-4 py-2 rounded-md mb-4">
              {error}
            </div>
          )}
          
          <SmsAlertsTable
            smsAlerts={smsAlerts}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={setPage}
          />
        </CardContent>
      </Card>
    </div>
  );
}
